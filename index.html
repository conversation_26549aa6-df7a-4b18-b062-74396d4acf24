<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keyboards" content="Android, Tool Software, Keyboard and Mouse Sharing, PC Software">
    <meta name="description" content="A seamless way to share keyboard and mouse of PC with Android device">
    <title>InputShare -- Effortless Android Device Control</title>
    <link rel="shortcut icon" href="./ui/icon.ico" type="image/x-icon">
    <style>
        * {
            box-sizing: border-box;
        }

        :root {
            --device-border-color: #666;
            --device-btm-color: #b9b9b9;
            --device-btm-before-color: #e3e3e3;
            --device-content-color: #e5e6e8;
            --device-content-background: #fff;
            --color-text-h1-shadow: rgba(0, 0, 0, 0.07);
            --color-text-h2-shadow: rgba(0, 0, 0, 0.05);
            --color-text-h1: #333;
            --color-text-h2: #333;
            --color-text-h3: #333;
            --color-text-p: #7a7b7e;
            --color-button-download: #000;
            --color-button-download-text: #fff;
            --color-gradient-start: #fafafa;
            --color-gradient-end: #ffffff;
            --color-device-btm-before-shadow: #999;
            --color-action-bar: #ccc;
            --color-content-wrapper: #cbcdd2;
            --color-feature-subtitle: #000;
            --color-feature-item: #666;
            --color-note-subtitle: rgb(180, 83, 9);
            --color-note-border: rgb(245 158 11);
            --color-note-background: rgb(255 251 235);
            --color-shortcuts-table-border: #e5e7eb;
            --color-shortcuts-table-background: rgba(255, 255, 255, 0.8);
            --color-shortcuts-thead-background: #f9fafb;
            --color-shortcuts-th-text: #6b7280;
            --color-shortcuts-td-text: #1f2937;
            --color-shortcuts-td-secondary-text: #303c55;
            --color-icon-container-background: #f3f4f6;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                color: white;
                background-color: #1a1a1a;

                --device-border-color: #ccc;
                --device-btm-color: #999;
                --device-btm-before-color: #555;
                --device-content-color: #666;
                --device-content-background: #2b2b2b;
                --color-text-h1-shadow: rgba(255, 255, 255, 0.1);
                --color-text-h2-shadow: rgba(255, 255, 255, 0.1);
                --color-text-h1: #f1f1f1;
                --color-text-h2: #f1f1f1;
                --color-text-h3: #f1f1f1;
                --color-text-p: #d4d4d4;
                --color-button-download: #1a1a1a;
                --color-button-download-text: #fff;
                --color-gradient-start: #1e1e1e;
                --color-gradient-end: #2a2a2a;
                --color-device-btm-before-shadow: #111;
                --color-action-bar: #333;
                --color-content-wrapper: #333;
                --color-feature-subtitle: #fff;
                --color-feature-item: #d4d4d4;
                --color-note-subtitle: #ffc107;
                --color-note-border: #ff9800;
                --color-note-background: #763a05;
                --color-shortcuts-table-border: #444;
                --color-shortcuts-table-background: rgba(255, 255, 255, 0.04);
                --color-shortcuts-thead-background: #333;
                --color-shortcuts-th-text: #ddd;
                --color-shortcuts-td-text: #f1f1f1;
                --color-shortcuts-td-secondary-text: #ddd;
                --color-icon-container-background: #444;
            }
        }

        html {
            font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
        }

        body {
            margin: 0;
        }

        h1 {
            margin-top: 0;
            margin-bottom: 1rem;
            font-size: 3rem;
            line-height: 1;
            color: var(--color-text-h1);
            filter: drop-shadow(var(--color-text-h1-shadow)) drop-shadow(var(--color-text-h1-shadow));
        }

        h2 {
            font-size: 2rem;
            color: var(--color-text-h2);
            filter: drop-shadow(var(--color-text-h2-shadow)) drop-shadow(var(--color-text-h2-shadow));
        }

        h3 {
            font-size: 1.6rem;
            color: var(--color-text-h3);
            margin: 0 0 1.5rem;
        }

        p {
            margin-bottom: 2rem;
            font-size: 1.375rem;
            line-height: 1.75rem;
            color: var(--color-text-p);
        }

        svg {
            width: 100%;
            height: 100%;
        }

        kbd {
            font-family: ui-monospace, consolas, monospace;
        }

        p.logo {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--color-text-h1);
            font-weight: bold;
        }

        a.download {
            font-size: 14px;
            padding: .5rem 2rem;
            border-radius: .5rem;
            color: var(--color-button-download-text);
            background-color: var(--color-button-download);
            cursor: pointer;
            border: none;
            text-decoration: none;
        }

        .hero-container {
            position: relative;
            overflow: hidden;
            background: linear-gradient(to top, var(--color-gradient-start), var(--color-gradient-end));
            margin-bottom: 3rem;
        }

        .hero-container .gradient-wrapper {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-image: url("data:image/svg+xml,%3Csvg%20width=%221097%22%20height=%22942%22%20viewBox=%220%200%201097%20942%22%20fill=%22none%22%20xmlns=%22http://www.w3.org/2000/svg%22%3E%3Cg%20opacity=%220.3%22%20clip-path=%22url(%23clip0_3_6)%22%3E %3Cg%20filter=%22url(%23filter0_f_3_6)%22%3E %3Cpath%20fill-rule=%22evenodd%22%20clip-rule=%22evenodd%22%20d=%22M543.5%20146C681.483%20144.69%20814.712%20232.089%20857.442%20363.296C900.242%20494.719%20817.835%20624.295%20706.979%20706.847C590.96%20793.243%20436.909%20847.868%20318.712%20764.477C196.644%20678.355%20177.294%20509.609%20228.498%20369.268C274.204%20243.998%20410.159%20147.266%20543.5%20146Z%22%20fill=%22url(%23paint0_linear_3_6)%22/%3E %3C/g%3E %3Cg%20filter=%22url(%23filter1_f_3_6)%22%3E %3Cpath%20fill-rule=%22evenodd%22%20clip-rule=%22evenodd%22%20d=%22M543.435%20213.179C593.895%20195.222%20642.188%20125.199%20689.441%20150.416C738.816%20176.765%20708.67%20259.464%20728.346%20311.857C740.695%20344.741%20782.605%20365.287%20783.325%20400.407C784.087%20437.591%20728.718%20461.157%20732.166%20498.188C736.843%20548.412%20805.413%20578.715%20804.624%20629.149C803.944%20672.557%20774.524%20712.215%20745.5%20744.5C711.799%20781.987%20669.583%20838.91%20619.304%20835.289C560.903%20831.084%20536.584%20755.083%20492.901%20716.094C468.011%20693.878%20441.011%20675.838%20415.966%20653.798C392.737%20633.355%20371.893%20611.807%20349.79%20590.153C321.547%20562.484%20259.331%20546.317%20266.583%20507.45C275.262%20460.936%20376.588%20472.573%20380.527%20425.42C386.126%20358.415%20268.497%20312.833%20288.215%20248.551C302.979%20200.417%20387.497%20247.031%20437.367%20240.119C473.899%20235.056%20508.688%20225.544%20543.435%20213.179Z%22%20fill=%22url(%23paint1_linear_3_6)%22/%3E %3C/g%3E %3Cg%20filter=%22url(%23filter2_f_3_6)%22%3E %3Cpath%20fill-rule=%22evenodd%22%20clip-rule=%22evenodd%22%20d=%22M533.115%20226.16C582.418%20231.123%20637.334%20207.48%20679.012%20234.281C720.661%20261.063%20726.246%20318.559%20746.835%20363.591C765.488%20404.392%20843.851%20381.545%20855%20425C869.193%20480.321%20856.533%20608.446%20821.159%20653.285C783.494%20701.028%20699.66%20654.789%20643.59%20678.331C597.85%20697.535%20580.527%20759.627%20533.115%20774.222C481.654%20790.064%20434.547%20767.861%20384.5%20748C330.413%20726.536%20274.851%20709.414%20259.5%20653.285C243.303%20594.062%20297.94%20548.348%20299.93%20486.983C301.817%20428.798%20263.197%20379.056%20277%20322.5C290.646%20266.587%20320.409%20252.857%20374.883%20234.281C428.008%20216.166%20477.269%20220.539%20533.115%20226.16Z%22%20fill=%22url(%23paint2_linear_3_6)%22/%3E %3C/g%3E %3C/g%3E %3Cdefs%3E %3Cfilter%20id=%22filter0_f_3_6%22%20x=%222%22%20y=%22-54.0145%22%20width=%221066.95%22%20height=%221059.89%22%20filterUnits=%22userSpaceOnUse%22%20color-interpolation-filters=%22sRGB%22%3E %3CfeFlood%20flood-opacity=%220%22%20result=%22BackgroundImageFix%22/%3E %3CfeBlend%20mode=%22normal%22%20in=%22SourceGraphic%22%20in2=%22BackgroundImageFix%22%20result=%22shape%22/%3E %3CfeGaussianBlur%20stdDeviation=%22100%22%20result=%22effect1_foregroundBlur_3_6%22/%3E %3C/filter%3E %3Cfilter%20id=%22filter1_f_3_6%22%20x=%2266%22%20y=%22-55%22%20width=%22938.631%22%20height=%221090.45%22%20filterUnits=%22userSpaceOnUse%22%20color-interpolation-filters=%22sRGB%22%3E %3CfeFlood%20flood-opacity=%220%22%20result=%22BackgroundImageFix%22/%3E %3CfeBlend%20mode=%22normal%22%20in=%22SourceGraphic%22%20in2=%22BackgroundImageFix%22%20result=%22shape%22/%3E %3CfeGaussianBlur%20stdDeviation=%22100%22%20result=%22effect1_foregroundBlur_3_6%22/%3E %3C/filter%3E %3Cfilter%20id=%22filter2_f_3_6%22%20x=%2238.503%22%20y=%223.24184%22%20width=%221040.2%22%20height=%22994.371%22%20filterUnits=%22userSpaceOnUse%22%20color-interpolation-filters=%22sRGB%22%3E %3CfeFlood%20flood-opacity=%220%22%20result=%22BackgroundImageFix%22/%3E %3CfeBlend%20mode=%22normal%22%20in=%22SourceGraphic%22%20in2=%22BackgroundImageFix%22%20result=%22shape%22/%3E %3CfeGaussianBlur%20stdDeviation=%22109%22%20result=%22effect1_foregroundBlur_3_6%22/%3E %3C/filter%3E %3ClinearGradient%20id=%22paint0_linear_3_6%22%20x1=%22-102.653%22%20y1=%22485.435%22%20x2=%22538.185%22%20y2=%221133.14%22%20gradientUnits=%22userSpaceOnUse%22%3E %3Cstop%20stop-color=%22%236964DE%22/%3E %3Cstop%20offset=%221%22%20stop-color=%22%23FCA6E9%22/%3E %3C/linearGradient%3E %3ClinearGradient%20id=%22paint1_linear_3_6%22%20x1=%2219.9628%22%20y1=%22500.173%22%20x2=%22670.368%22%20y2=%221007.56%22%20gradientUnits=%22userSpaceOnUse%22%3E %3Cstop%20stop-color=%22%236964DE%22/%3E %3Cstop%20offset=%221%22%20stop-color=%22%23FCA6E9%22/%3E %3C/linearGradient%3E %3ClinearGradient%20id=%22paint2_linear_3_6%22%20x1=%22494.144%22%20y1=%221040.59%22%20x2=%221092.31%22%20y2=%22517.626%22%20gradientUnits=%22userSpaceOnUse%22%3E %3Cstop%20stop-color=%22%239358F7%22/%3E %3Cstop%20offset=%220.0666667%22%20stop-color=%22%239259F7%22/%3E %3Cstop%20offset=%220.133333%22%20stop-color=%22%238E5DF6%22/%3E %3Cstop%20offset=%220.2%22%20stop-color=%22%238862F5%22/%3E %3Cstop%20offset=%220.266667%22%20stop-color=%22%23806BF4%22/%3E %3Cstop%20offset=%220.333333%22%20stop-color=%22%237575F2%22/%3E %3Cstop%20offset=%220.4%22%20stop-color=%22%236882F0%22/%3E %3Cstop%20offset=%220.466667%22%20stop-color=%22%235990EE%22/%3E %3Cstop%20offset=%220.533333%22%20stop-color=%22%234A9FEB%22/%3E %3Cstop%20offset=%220.6%22%20stop-color=%22%233BADE9%22/%3E %3Cstop%20offset=%220.666667%22%20stop-color=%22%232EBAE7%22/%3E %3Cstop%20offset=%220.733333%22%20stop-color=%22%2323C4E5%22/%3E %3Cstop%20offset=%220.8%22%20stop-color=%22%231BCDE4%22/%3E %3Cstop%20offset=%220.866667%22%20stop-color=%22%2315D2E3%22/%3E %3Cstop%20offset=%220.933333%22%20stop-color=%22%2311D6E2%22/%3E %3Cstop%20offset=%221%22%20stop-color=%22%2310D7E2%22/%3E %3C/linearGradient%3E %3CclipPath%20id=%22clip0_3_6%22%3E %3Crect%20width=%221587%22%20height=%22960%22%20fill=%22white%22%20transform=%22translate(-490)%22/%3E %3C/clipPath%3E %3C/defs%3E %3C/svg%3E ");
            background-position-x: left;
            background-position-y: bottom;
            opacity: .5;
            pointer-events: none;
        }

        .hero {
            margin: 0 2rem 3rem;
        }

        .hero .content {
            max-width: 48rem;
        }

        .hero nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 1rem 0 8rem;
        }

        .hero nav p {
            margin: 0;
        }

        .hero a.github {
            position: relative;
            bottom: 0;
            padding-bottom: 0;
            color: var(--color-text-h1) !important;
            font-weight: bold;
            text-decoration: none;
            background-image: linear-gradient(
                to right,
                var(--color-text-h1),
                var(--color-text-h1)
            );
            background-repeat: no-repeat;
            background-position: right bottom;
            background-size: 0 2px;
            transition: bottom .3s,
                        padding .15s,
                        background-size .3s;
        }
        .hero a.github:hover {
            bottom: 2px;
            padding-bottom: 2px;
            background-position: left bottom;
            background-size: 100% 2px;
        }

        .hero .action span {
            margin-left: 1rem;
            color: var(--color-text-p);
            font-size: .85rem;
        }

        .device-container {
            display: flex;
            justify-content: center;
            gap: 6rem;
        }

        
        @media screen and (max-width: 768px) {
            .hero p {
                font-size: 1.125rem;
            }
        }

        @media screen and (min-width: 1024px) {
            h1 {
                font-size: 3.75rem;
            }
            .hero nav {
                margin-bottom: min(14rem, 40vh);
            }
            .hero-container .gradient-wrapper {
                background-position-x: 16rem !important;
            }
        }

        @media screen and (max-width: 768px) {
            .device-container {
                flex-direction: column;
                align-items: center;
                gap: 1rem;
            }

            .device-description {
                margin-top: 1rem !important;
            }

            .device-description .text {
                width: 8rem !important;
            }
        }

        .laptop {
            position: relative;
            width: 235px;
            height: 155px;
            background: var(--device-content-background);
            border: 12px solid var(--device-border-color);
            border-radius: 10px 10px 0 0;
        }

        .laptop .content {
            width: 100%;
            height: 100%;
            margin-left: -210px;
            overflow: hidden;
        }

        .laptop .content>.item-line {
            width: 189px;
            height: 16px;
            background-color: var(--device-content-color);
            margin: 8px 11px;
        }

        .laptop .content>.item-block {
            width: 189px;
            height: 40px;
            background-color: var(--device-content-color);
            margin: 8px 11px;
        }

        .laptop .content>.items {
            display: flex;
            justify-content: space-between;
            margin: 8px 11px;
        }

        .laptop .content>.items>.item {
            width: 58px;
            height: 40px;
            background-color: var(--device-content-color);
        }

        .laptop .cursor {
            position: absolute;
            top: 16px;
            left: 16px;
            animation: laptop-cursor 10s infinite;
            z-index: 1;
        }

        @keyframes laptop-cursor {
            0% {
                top: 16px;
                left: 16px;
            }

            12.5% {
                top: 16px;
                left: 180px;
            }

            25% {
                top: 100px;
                left: 180px;
            }

            37.5% {
                top: 100px;
                left: 16px;
            }

            50% {
                top: 16px;
                left: 16px;
            }

            100% {
                top: 16px;
                left: 16px;
            }
        }

        .laptop>.content {
            width: 211px;
            height: 131px;
            left: 0;
            margin-left: 0;
        }

        .btm {
            position: relative;
            width: 296px;
            height: 7px;
            margin-top: 10px;
            margin-left: -42px;
            border-radius: 0 0 20px 20px;
            background: var(--device-btm-color);
        }

        .btm:before {
            content: "";
            position: absolute;
            width: 42px;
            height: 3px;
            left: 50%;
            top: 0;
            margin-left: -21px;
            border-radius: 0 0 5px 5px;
            background: var(--device-btm-before-color);
        }

        .tablet {
            position: relative;
            width: 180px;
            height: 110px;
            margin-top: 24px;
            border: solid 8px var(--device-border-color);
            border-radius: 6px;
        }

        .tablet .cursor {
            position: absolute;
            transform: scale(.8);
            animation: tablet-cursor 10s infinite;
        }

        @keyframes tablet-cursor {
            0% {
                top: 10px;
                left: 12px;
            }

            50% {
                top: 10px;
                left: 12px;
            }

            62.5% {
                top: 10px;
                left: 136px;
            }

            75% {
                top: 64px;
                left: 136px;
            }

            87.5% {
                top: 64px;
                left: 12px;
            }

            100% {
                top: 10px;
                left: 12px;
            }
        }

        .tablet .camara {
            position: absolute;
            top: -6px;
            left: 77px;
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background-color: var(--color-device-btm-before-shadow);
        }

        .tablet .action-bar {
            position: absolute;
            left: 40px;
            bottom: 2px;
            width: 80px;
            height: 2px;
            background-color: var(--color-action-bar);
            border-radius: 1px;
        }

        .tablet .content-wrapper {
            width: 100%;
            height: 100%;
            background-color: var(--color-content-wrapper);
        }

        .tablet .content {
            width: 100%;
            height: 100%;
            border-radius: 3px;
            background-color: var(--device-content-background);
            overflow: hidden;
        }

        .tablet .content>.item-block {
            width: 143px;
            height: 34px;
            background-color: var(--device-content-color);
            margin: 8px 11px;
        }

        .tablet .content>.items {
            display: flex;
            justify-content: space-between;
            margin: 8px 11px;
        }

        .tablet .content>.items>.item {
            width: 42px;
            height: 34px;
            background-color: var(--device-content-color);
        }

        .device-description {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 2rem;
            margin-bottom: 4rem;
        }

        .device-description .text {
            position: relative;
            line-height: 36px;
            font-weight: bold;
            font-size: 1.5rem;
            width: 9rem;
        }

        .device-description .text>* {
            position: absolute;
            top: 0;
            right: 0;
        }

        .device-description .text .disabled {
            color: var(--device-content-color);
            animation: switch-text-disabled 10s infinite;
        }

        .device-description .text .enabled {
            opacity: 0;
            color: var(--color-text-p);
            animation: switch-text-enabled 10s infinite;
        }

        @keyframes switch-text-disabled {
            0% {}

            45% {
                opacity: 1;
            }

            50% {
                opacity: 0;
            }

            95% {
                opacity: 0;
            }

            100% {
                opacity: 1;
            }

        }

        @keyframes switch-text-enabled {
            0% {}

            45% {
                opacity: 0;
            }

            50% {
                opacity: 1;
            }

            95% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }

        }

        label {
            width: 72px;
            height: 36px;
            background: var(--device-content-color);
            display: block;
            border-radius: 100px;
            position: relative;
            transition: .3s;
            animation: switch-label 10s infinite;
        }

        label::after {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 30px;
            height: 30px;
            background: #fff;
            border-radius: 90px;
            transition: 0.3s;
            animation: switch-label-ball 10s infinite;
        }

        @keyframes switch-label {
            0% {}

            45% {
                background: var(--device-content-color);
            }

            50% {
                background: var(--color-text-p);
            }

            95% {
                background: var(--color-text-p);
            }

            100% {
                background: var(--device-content-color);
            }

        }

        @keyframes switch-label-ball {
            0% {}

            45% {
                width: 30px;
                left: 3px;
            }

            47.5% {
                width: 36px;
            }

            50% {
                width: 30px;
                left: calc(100% - 33px);
            }

            95% {
                width: 30px;
                left: calc(100% - 33px);
            }

            97.5% {
                width: 36px;
            }

            100% {
                width: 30px;
                left: 3px;
            }
        }

        .features {
            margin: 0 1.5rem 4rem;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .feature-item {
            color: var(--color-feature-item);
            line-height: 1.5;
        }

        .feature-subtitle {
            display: flex;
            align-items: center;
            gap: 6px;
            color: var(--color-feature-subtitle);
            margin-bottom: .5rem;
            font-size: 1.025rem;
        }

        .feature-icon {
            width: 20px;
            height: 20px;
        }

        @media screen and (min-width: 1024px) {
            .features {
                display: flex;
                align-items: baseline;
                gap: 18rem;
            }
        }

        .how-to-use {
            margin: 0 1.5rem 4rem;
        }

        .note {
            margin-right: -1.5rem;
            margin-bottom: 2rem;
            padding: 1rem;
            border-left: solid 4px var(--color-note-border);
            background-color: var(--color-note-background);
        }

        .note-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: bold;
            margin-bottom: 6px;
            color: var(--color-note-subtitle);
        }

        .note-title .icon {
            width: 18px;
            height: 18px;
        }

        .note p {
            margin: 0;
            font-size: 1rem;
            line-height: 1.5rem;
            color: var(--color-text-h1);
        }

        .how-to-use h3 {
            display: flex;
            align-items: center;
            gap: .6rem;
        }

        .how-to-use .content .icon {
            display: flex;
            width: 1.5rem;
            height: 1.5rem;
        }

        .how-to-use ol {
            padding: 0;
            margin-bottom: 2rem;
        }

        .how-to-use li::marker {
            content: none;
        }

        .how-to-use li {
            display: flex;
            margin-bottom: 1rem;
            line-height: 24px;
        }

        .how-to-use li .counter {
            flex-shrink: 0;
            width: 1.5rem;
            height: 1.5rem;
            color: white;
            text-align: center;
            line-height: 1.5rem;
            border-radius: 50%;
            margin-right: 1rem;
            background-color: var(--color-button-download);
        }

        @media screen and (min-width: 1024px) {
            .how-to-use .content {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 4rem;
            }
        }

        .shortcuts {
            margin: 0 1.5rem 4rem;
            overflow-x: hidden;
        }

        .shortcuts p {
            font-size: 1rem;
            margin-bottom: .8rem;
        }

        .shortcuts .table-wrapper {
            overflow-x: auto;
        }
        .shortcuts table {
            width: 100%;
            margin-bottom: 1.2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
            border: 1px solid var(--color-shortcuts-table-border);
            background-color: var(--color-shortcuts-table-background);
            border-collapse: collapse;
            overflow: hidden;
        }

        .shortcuts thead {
            background-color: var(--color-shortcuts-thead-background);
        }

        .shortcuts th {
            padding: 12px 24px;
            text-align: left;
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--color-shortcuts-th-text);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .shortcuts tbody {
            border-top: 1px solid var(--color-shortcuts-table-border);
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
        }

        .shortcuts tr:hover {
            background-color: var(--color-shortcuts-thead-background);
        }

        .shortcuts td {
            padding: 16px 24px;
            white-space: nowrap;
            font-size: 0.875rem;
            color: var(--color-shortcuts-td-text);
        }

        .shortcuts td:nth-child(2),
        .shortcuts td:nth-child(3) {
            color: var(--color-shortcuts-td-secondary-text);
        }

        .icon-container {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background-color: var(--color-icon-container-background);
            border-radius: 50%;
        }

        .icon-container svg {
            width: 60%;
            height: 60%;
        }

        @media screen and (min-width: 1096px) {
            .shortcuts {
                display: flex;
                align-items: baseline;
                justify-content: space-between;
            }

            .shortcuts table {
                width: auto;
            }

            .shortcuts h2 {
                margin-right: 4rem;
            }

            .shortcuts .content {
                display: flex;
                gap: 2rem;
            }
        }
    </style>
</head>

<body>
    <div class="hero-container">
        <div class="gradient-wrapper"></div>
        <div class="hero">
            <nav>
                <p class="logo">
                    <img src="./ui/icon.png" width="36" height="36">InputShare
                </p>
                <a class="github" href="https://github.com/BHznJNs/InputShare">GitHub</a>
            </nav>
            <div class="content">
                <h1>Effortless Android Device Control</h1>
                <p>Seamlessly share your computer's keyboard, mouse and clipboard with Android device using InputShare.</p>
                <div class="action">
                    <a class="download"
                        href="https://github.com/BHznJNs/InputShare/releases/latest">Download</a><span>Free
                        and open-source.</span>
                </div>
            </div>
        </div>
    </div>

    <div class="device-container">
        <div class="laptop">
            <div class="cursor">
                <svg width="16" height="16" fill="currentColor" class="bi bi-cursor-fill" viewBox="0 0 16 16"
                    version="1.1" id="svg11" sodipodi:docname="cursor-fill.svg"
                    inkscape:version="1.2.2 (732a01da63, 2022-12-09)"
                    xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
                    xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
                    xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg">
                    <defs id="defs15" />
                    <sodipodi:namedview id="namedview13" pagecolor="#ffffff" bordercolor="#000000" borderopacity="0.25"
                        inkscape:showpageshadow="2" inkscape:pageopacity="0.0" inkscape:pagecheckerboard="0"
                        inkscape:deskcolor="#d1d1d1" showgrid="false" inkscape:zoom="61.375" inkscape:cx="9.4826884"
                        inkscape:cy="6.9246436" inkscape:window-width="2240" inkscape:window-height="1276"
                        inkscape:window-x="-12" inkscape:window-y="-12" inkscape:window-maximized="1"
                        inkscape:current-layer="svg11" />
                    <path
                        d="M 0.6598796,0.65953903 A 0.54633836,0.54632453 0 0 0 0.5473336,1.2681446 l 6.181272,13.9072384 a 0.54633836,0.54632453 0 0 0 1.0019845,-0.0076 L 9.9607433,9.9601683 15.169533,7.7289785 a 0.54633836,0.54632453 0 0 0 0.0066,-1.0008665 L 1.2685006,0.54699613 a 0.54633836,0.54632453 0 0 0 -0.607528,0.1125429 z"
                        id="path9" style="stroke-width:1.09266" />
                </svg>
            </div>
            <div class="content">
                <div class="item-line"></div>
                <div class="item-block"></div>
                <div class="items">
                    <div class="item"></div>
                    <div class="item"></div>
                    <div class="item"></div>
                </div>
            </div>
            <div class="btm"></div>
        </div>

        <div class="tablet">
            <div class="cursor">
                <svg width="16" height="16" fill="currentColor" class="bi bi-cursor-fill" viewBox="0 0 16 16"
                    version="1.1" id="svg11" sodipodi:docname="cursor-fill.svg"
                    inkscape:version="1.2.2 (732a01da63, 2022-12-09)"
                    xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
                    xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
                    xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg">
                    <defs id="defs15" />
                    <sodipodi:namedview id="namedview13" pagecolor="#ffffff" bordercolor="#000000" borderopacity="0.25"
                        inkscape:showpageshadow="2" inkscape:pageopacity="0.0" inkscape:pagecheckerboard="0"
                        inkscape:deskcolor="#d1d1d1" showgrid="false" inkscape:zoom="61.375" inkscape:cx="9.4826884"
                        inkscape:cy="6.9246436" inkscape:window-width="2240" inkscape:window-height="1276"
                        inkscape:window-x="-12" inkscape:window-y="-12" inkscape:window-maximized="1"
                        inkscape:current-layer="svg11" />
                    <path
                        d="M 0.6598796,0.65953903 A 0.54633836,0.54632453 0 0 0 0.5473336,1.2681446 l 6.181272,13.9072384 a 0.54633836,0.54632453 0 0 0 1.0019845,-0.0076 L 9.9607433,9.9601683 15.169533,7.7289785 a 0.54633836,0.54632453 0 0 0 0.0066,-1.0008665 L 1.2685006,0.54699613 a 0.54633836,0.54632453 0 0 0 -0.607528,0.1125429 z"
                        id="path9" style="stroke-width:1.09266" />
                </svg>
            </div>
            <div class="camara"></div>
            <div class="content-wrapper">
                <div class="content">
                    <div class="item-block"></div>
                    <div class="items">
                        <div class="item"></div>
                        <div class="item"></div>
                        <div class="item"></div>
                    </div>
                </div>
            </div>
            <div class="action-bar"></div>
        </div>
    </div>
    <div class="device-description">
        <div class="text">
            <span class="disabled">Disabled</span>
            <span class="enabled">Enabled</span>
        </div>
        <label for="switch"></label>
    </div>

    <div class="features">
        <h2>Features</h2>
        <div class="feature-grid">
            <div class="feature-item">
                <div class="feature-subtitle">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                            class="bi bi-toggles" viewBox="0 0 16 16">
                            <path
                                d="M4.5 9a3.5 3.5 0 1 0 0 7h7a3.5 3.5 0 1 0 0-7h-7zm7 6a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5zm-7-14a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zm2.45 0A3.49 3.49 0 0 1 8 3.5 3.49 3.49 0 0 1 6.95 6h4.55a2.5 2.5 0 0 0 0-5H6.95zM4.5 0h7a3.5 3.5 0 1 1 0 7h-7a3.5 3.5 0 1 1 0-7z" />
                        </svg>
                    </div>Seamless Switching
                </div>Toggle control between PC and Android with a single hotkey.
            </div>
            <div class="feature-item">
                <div class="feature-subtitle">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                            class="bi bi-wifi" viewBox="0 0 16 16">
                            <path
                                d="M15.384 6.115a.485.485 0 0 0-.047-.736A12.444 12.444 0 0 0 8 3C5.259 3 2.723 3.882.663 5.379a.485.485 0 0 0-.048.736.518.518 0 0 0 .668.05A11.448 11.448 0 0 1 8 4c2.507 0 4.827.802 6.716 *************.49.13.668-.049z" />
                            <path
                                d="M13.229 8.271a.482.482 0 0 0-.063-.745A9.455 9.455 0 0 0 8 6c-1.905 0-3.68.56-5.166 1.526a.48.48 0 0 0-.063.745.525.525 0 0 0 .652.065A8.46 8.46 0 0 1 8 7a8.46 8.46 0 0 1 4.576 1.336c.206.132.48.108.653-.065zm-2.183 2.183c.226-.226.185-.605-.1-.75A6.473 6.473 0 0 0 8 9c-1.06 0-2.062.254-2.946.704-.285.145-.326.524-.1.75l.015.015c.16.16.407.19.611.09A5.478 5.478 0 0 1 8 10c.868 0 1.69.201 2.42.56.203.1.45.07.61-.091l.016-.015zM9.06 12.44c.196-.196.198-.52-.04-.66A1.99 1.99 0 0 0 8 11.5a1.99 1.99 0 0 0-1.02.28c-.238.14-.236.464-.04.66l.706.706a.5.5 0 0 0 .707 0l.707-.707z" />
                        </svg>
                    </div>Flexible Connections
                </div>Supports both USB and wireless connections.
            </div>
            <div class="feature-item">
                <div class="feature-subtitle">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                            class="bi bi-clipboard" viewBox="0 0 16 16">
                            <path
                                d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z" />
                            <path
                                d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z" />
                        </svg>
                    </div>Clipboard Sync
                </div>Copy and paste text between devices instantly.
            </div>
            <div class="feature-item">
                <div class="feature-subtitle">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                            class="bi bi-ui-radios-grid" viewBox="0 0 16 16">
                            <path
                                d="M3.5 15a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5zm9-9a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5zm0 9a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5zM16 3.5a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0zm-9 9a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0zm5.5 3.5a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7zm-9-11a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm0 2a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7z" />
                        </svg>
                    </div>Intuitive User Interface
                </div>Simple setup and easy-to-navigate interface.
            </div>
        </div>
    </div>

    <div class="how-to-use">
        <h2>How to Use</h2>
        <div class="note">
            <div class="note-title">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                        class="bi bi-gear" viewBox="0 0 16 16">
                        <path
                            d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z" />
                        <path
                            d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.291-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.291c.415.764-.42 1.6-1.185 1.184l-.291-.159a1.873 1.873 0 0 0-2.693 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.692-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.291A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115l.094-.319z" />
                    </svg>
                </div>Note:
            </div>
            <p>You firstly need to enable the Developer Settings of your Android device.</p>
        </div>

        <div class="content">
            <div>
                <h3>
                    <div class="icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                            class="bi bi-usb-symbol" viewBox="0 0 16 16">
                            <path
                                d="m7.792.312-1.533 2.3A.25.25 0 0 0 6.467 3H7.5v7.319a2.5 2.5 0 0 0-.515-.298L5.909 9.56A1.5 1.5 0 0 1 5 8.18v-.266a1.5 1.5 0 1 0-1 0v.266a2.5 2.5 0 0 0 1.515 2.298l1.076.461a1.5 1.5 0 0 1 .888 1.129 2.001 2.001 0 1 0 1.021-.006v-.902a1.5 1.5 0 0 1 .756-1.303l1.484-.848A2.5 2.5 0 0 0 11.995 7h.755a.25.25 0 0 0 .25-.25v-2.5a.25.25 0 0 0-.25-.25h-2.5a.25.25 0 0 0-.25.25v2.5c0 .138.112.25.25.25h.741a1.5 1.5 0 0 1-.747 1.142L8.76 8.99a2.584 2.584 0 0 0-.26.17V3h1.033a.25.25 0 0 0 .208-.389L8.208.312a.25.25 0 0 0-.416 0Z" />
                        </svg>
                    </div>Wired Connection
                </h3>
                <ol class="wired">
                    <li>
                        <div class="counter">1</div>Enable the USB Debugging in the Developer Settings page
                    </li>
                    <li>
                        <div class="counter">2</div>Connect your device with computer via a USB cable
                    </li>
                    <li>
                        <div class="counter">3</div>Just run the executable and skip the pairing and connecting steps
                    </li>
                    <li>
                        <div class="counter">4</div>Enjoy your mouse and keyboard on Android device!
                    </li>
                </ol>
            </div>

            <div>
                <h3>
                    <div class="icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                            class="bi bi-wifi" viewBox="0 0 16 16">
                            <path
                                d="M15.384 6.115a.485.485 0 0 0-.047-.736A12.444 12.444 0 0 0 8 3C5.259 3 2.723 3.882.663 5.379a.485.485 0 0 0-.048.736.518.518 0 0 0 .668.05A11.448 11.448 0 0 1 8 4c2.507 0 4.827.802 6.716 *************.49.13.668-.049z" />
                            <path
                                d="M13.229 8.271a.482.482 0 0 0-.063-.745A9.455 9.455 0 0 0 8 6c-1.905 0-3.68.56-5.166 1.526a.48.48 0 0 0-.063.745.525.525 0 0 0 .652.065A8.46 8.46 0 0 1 8 7a8.46 8.46 0 0 1 4.576 1.336c.206.132.48.108.653-.065zm-2.183 2.183c.226-.226.185-.605-.1-.75A6.473 6.473 0 0 0 8 9c-1.06 0-2.062.254-2.946.704-.285.145-.326.524-.1.75l.015.015c.16.16.407.19.611.09A5.478 5.478 0 0 1 8 10c.868 0 1.69.201 2.42.56.203.1.45.07.61-.091l.016-.015zM9.06 12.44c.196-.196.198-.52-.04-.66A1.99 1.99 0 0 0 8 11.5a1.99 1.99 0 0 0-1.02.28c-.238.14-.236.464-.04.66l.706.706a.5.5 0 0 0 .707 0l.707-.707z" />
                        </svg>
                    </div>Wireless connection
                </h3>
                <ol class="wireless">
                    <li>
                        <div class="counter">1</div>Enable the Wireless Debugging in the Developer Settings page
                    </li>
                    <li>
                        <div class="counter">2</div>Run the executable
                    </li>
                    <li>
                        <div class="counter">3</div>Input the IP address, port, and pairing code into the pairing tab of
                        the connecting window
                    </li>
                    <li>
                        <div class="counter">4</div>Input the IP address and port from the main Wireless Debugging into
                        the connecting tab
                    </li>
                    <li>
                        <div class="counter">5</div>Enjoy your mouse and keyboard on Android device!
                    </li>
                </ol>
            </div>
        </div>
    </div>

    <div class="shortcuts">
        <h2>Shortcuts</h2>

        <div class="content">
            <div class="shortcuts-sharing">
                <p>Shortcuts below are available while sharing:</p>
                <div class="table-wrapper">
                    <table>
                        <thead>
                            <tr>
                                <th>Shortcut</th>
                                <th>Description</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><kbd>Ctrl</kbd>+<kbd>Alt</kbd>+<kbd>S</kbd></td>
                                <td>Toggle the sharing state</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-toggles" viewBox="0 0 16 16">
                                            <path
                                                d="M4.5 9a3.5 3.5 0 1 0 0 7h7a3.5 3.5 0 1 0 0-7h-7zm7 6a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5zm-7-14a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zm2.45 0A3.49 3.49 0 0 1 8 3.5 3.49 3.49 0 0 1 6.95 6h4.55a2.5 2.5 0 0 0 0-5H6.95zM4.5 0h7a3.5 3.5 0 1 1 0 7h-7a3.5 3.5 0 1 1 0-7z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><kbd>Ctrl</kbd>+<kbd>Alt</kbd>+<kbd>Q</kbd></td>
                                <td>Quit the program</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-escape" viewBox="0 0 16 16">
                                            <path
                                                d="M8.538 1.02a.5.5 0 1 0-.076.998 6 6 0 1 1-6.445 6.444.5.5 0 0 0-.997.076A7 7 0 1 0 8.538 1.02Z" />
                                            <path
                                                d="M7.096 7.828a.5.5 0 0 0 .707-.707L2.707 2.025h2.768a.5.5 0 1 0 0-1H1.5a.5.5 0 0 0-.5.5V5.5a.5.5 0 0 0 1 0V2.732l5.096 5.096Z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><kbd>F1</kbd></td>
                                <td>Multi-task switching</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-grid-3x2" viewBox="0 0 16 16">
                                            <path
                                                d="M0 3.5A1.5 1.5 0 0 1 1.5 2h13A1.5 1.5 0 0 1 16 3.5v8a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 0 11.5v-8zM1.5 3a.5.5 0 0 0-.5.5V7h4V3H1.5zM5 8H1v3.5a.5.5 0 0 0 .5.5H5V8zm1 0v4h4V8H6zm4-1V3H6v4h4zm1 1v4h3.5a.5.5 0 0 0 .5-.5V8h-4zm0-1h4V3.5a.5.5 0 0 0-.5-.5H11v4z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><kbd>F2</kbd></td>
                                <td>Return to Home</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-house" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd"
                                                d="M8.707 1.5a1 1 0 0 0-1.414 0L.646 8.146a.5.5 0 0 0 .708.708L2 8.207V13.5A1.5 1.5 0 0 0 3.5 15h9a1.5 1.5 0 0 0 1.5-1.5V8.207l.646.647a.5.5 0 0 0 .708-.708L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293L8.707 1.5ZM13 7.207l-5-5-5 5V13.5a.5.5 0 0 0 .5.5h9a.5.5 0 0 0 .5-.5V7.207Z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><kbd>F3</kbd></td>
                                <td>Back</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-arrow-return-left" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd"
                                                d="M14.5 1.5a.5.5 0 0 1 .5.5v4.8a2.5 2.5 0 0 1-2.5 2.5H2.707l3.347 3.346a.5.5 0 0 1-.708.708l-4.2-4.2a.5.5 0 0 1 0-.708l4-4a.5.5 0 1 1 .708.708L2.707 8.3H12.5A1.5 1.5 0 0 0 14 6.8V2a.5.5 0 0 1 .5-.5z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><kbd>F4</kbd></td>
                                <td>Previous Media</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-skip-start" viewBox="0 0 16 16">
                                            <path
                                                d="M4 4a.5.5 0 0 1 1 0v3.248l6.267-3.636c.52-.302 1.233.043 1.233.696v7.384c0 .653-.713.998-1.233.696L5 8.752V12a.5.5 0 0 1-1 0V4zm7.5.633L5.696 8l5.804 3.367V4.633z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><kbd>F5</kbd></td>
                                <td>Play / Pause Media</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-play" viewBox="0 0 16 16">
                                            <path
                                                d="M10.804 8 5 4.633v6.734L10.804 8zm.792-.696a.802.802 0 0 1 0 1.392l-6.363 3.692C4.713 12.69 4 12.345 4 11.692V4.308c0-.653.713-.998 1.233-.696l6.363 3.692z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><kbd>F6</kbd></td>
                                <td>Next Media</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-skip-end" viewBox="0 0 16 16">
                                            <path
                                                d="M12.5 4a.5.5 0 0 0-1 0v3.248L5.233 3.612C4.713 3.31 4 3.655 4 4.308v7.384c0 .653.713.998 1.233.696L11.5 8.752V12a.5.5 0 0 0 1 0V4zM5 4.633 10.804 8 5 11.367V4.633z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><kbd>F7</kbd></td>
                                <td>Volume Down</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-volume-down" viewBox="0 0 16 16">
                                            <path
                                                d="M9 4a.5.5 0 0 0-.812-.39L5.825 5.5H3.5A.5.5 0 0 0 3 6v4a.5.5 0 0 0 .5.5h2.325l2.363 1.89A.5.5 0 0 0 9 12V4zM6.312 6.39 8 5.04v5.92L6.312 9.61A.5.5 0 0 0 6 9.5H4v-3h2a.5.5 0 0 0 .312-.11zM12.025 8a4.486 4.486 0 0 1-1.318 3.182L10 10.475A3.489 3.489 0 0 0 11.025 8 3.49 3.49 0 0 0 10 5.525l.707-.707A4.486 4.486 0 0 1 12.025 8z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><kbd>F8</kbd></td>
                                <td>Volume Up</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-volume-up" viewBox="0 0 16 16">
                                            <path
                                                d="M11.536 14.01A8.473 8.473 0 0 0 14.026 8a8.473 8.473 0 0 0-2.49-6.01l-.708.707A7.476 7.476 0 0 1 13.025 8c0 2.071-.84 3.946-2.197 5.303l.708.707z" />
                                            <path
                                                d="M10.121 12.596A6.48 6.48 0 0 0 12.025 8a6.48 6.48 0 0 0-1.904-4.596l-.707.707A5.483 5.483 0 0 1 11.025 8a5.483 5.483 0 0 1-1.61 3.89l.706.706z" />
                                            <path
                                                d="M10.025 8a4.486 4.486 0 0 1-1.318 3.182L8 10.475A3.489 3.489 0 0 0 9.025 8c0-.966-.392-1.841-1.025-2.475l.707-.707A4.486 4.486 0 0 1 10.025 8zM7 4a.5.5 0 0 0-.812-.39L3.825 5.5H1.5A.5.5 0 0 0 1 6v4a.5.5 0 0 0 .5.5h2.325l2.363 1.89A.5.5 0 0 0 7 12V4zM4.312 6.39 6 5.04v5.92L4.312 9.61A.5.5 0 0 0 4 9.5H2v-3h2a.5.5 0 0 0 .312-.11z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><kbd>F9</kbd></td>
                                <td>Brightness Down</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-brightness-low" viewBox="0 0 16 16">
                                            <path
                                                d="M8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6zm0 1a4 4 0 1 0 0-8 4 4 0 0 0 0 8zm.5-9.5a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0zm0 11a.5.5 0 1 1-1 0 .5.5 0 0 1 1 0zm5-5a.5.5 0 1 1 0-1 .5.5 0 0 1 0 1zm-11 0a.5.5 0 1 1 0-1 .5.5 0 0 1 0 1zm9.743-4.036a.5.5 0 1 1-.707-.707.5.5 0 0 1 .707.707zm-7.779 7.779a.5.5 0 1 1-.707-.707.5.5 0 0 1 .707.707zm7.072 0a.5.5 0 1 1 .707-.707.5.5 0 0 1-.707.707zM3.757 4.464a.5.5 0 1 1 .707-.707.5.5 0 0 1-.707.707z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><kbd>F10</kbd></td>
                                <td>Brightness Up</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-brightness-high" viewBox="0 0 16 16">
                                            <path
                                                d="M8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6zm0 1a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM8 0a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 0zm0 13a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 13zm8-5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2a.5.5 0 0 1 .5.5zM3 8a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2A.5.5 0 0 1 3 8zm10.657-5.657a.5.5 0 0 1 0 .707l-1.414 1.415a.5.5 0 1 1-.707-.708l1.414-1.414a.5.5 0 0 1 .707 0zm-9.193 9.193a.5.5 0 0 1 0 .707L3.05 13.657a.5.5 0 0 1-.707-.707l1.414-1.414a.5.5 0 0 1 .707 0zm9.193 2.121a.5.5 0 0 1-.707 0l-1.414-1.414a.5.5 0 0 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .707zM4.464 4.465a.5.5 0 0 1-.707 0L2.343 3.05a.5.5 0 1 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .708z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><kbd>F11</kbd></td>
                                <td>Screen Sleep</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-moon" viewBox="0 0 16 16">
                                            <path
                                                d="M6 .278a.768.768 0 0 1 .08.858 7.208 7.208 0 0 0-.878 3.46c0 4.021 3.278 7.277 7.318 7.277.527 0 1.04-.055 1.533-.16a.787.787 0 0 1 .81.316.733.733 0 0 1-.031.893A8.349 8.349 0 0 1 8.344 16C3.734 16 0 12.286 0 7.71 0 4.266 2.114 1.312 5.124.06A.752.752 0 0 1 6 .278zM4.858 1.311A7.269 7.269 0 0 0 1.025 7.71c0 4.02 3.279 7.276 7.319 7.276a7.316 7.316 0 0 0 5.205-2.162c-.337.042-.68.063-1.029.063-4.61 0-8.343-3.714-8.343-8.29 0-1.167.242-2.278.681-3.286z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><kbd>F12</kbd></td>
                                <td>Wake Up</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-power" viewBox="0 0 16 16">
                                            <path d="M7.5 1v7h1V1h-1z" />
                                            <path
                                                d="M3 8.812a4.999 4.999 0 0 1 2.578-4.375l-.485-.874A6 6 0 1 0 11 3.616l-.501.865A5 5 0 1 1 3 8.812z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>Mouse side button <kbd>X1</kbd></td>
                                <td>Back</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-arrow-return-left" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd"
                                                d="M14.5 1.5a.5.5 0 0 1 .5.5v4.8a2.5 2.5 0 0 1-2.5 2.5H2.707l3.347 3.346a.5.5 0 0 1-.708.708l-4.2-4.2a.5.5 0 0 1 0-.708l4-4a.5.5 0 1 1 .708.708L2.707 8.3H12.5A1.5 1.5 0 0 0 14 6.8V2a.5.5 0 0 1 .5-.5z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>Mouse side button <kbd>X2</kbd></td>
                                <td>Open Notification</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#000">
                                            <path
                                                d="M160-200v-80h80v-280q0-83 50-147.5T420-792v-28q0-25 17.5-42.5T480-880q25 0 42.5 17.5T540-820v28q80 20 130 84.5T720-560v280h80v80H160Zm320-300Zm0 420q-33 0-56.5-23.5T400-160h160q0 33-23.5 56.5T480-80ZM320-280h320v-280q0-66-47-113t-113-47q-66 0-113 47t-47 113v280Z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="shortcuts-not-sharing">
                <p>Shortcuts below are available while not sharing:</p>
                <div class="table-wrapper">
                    <table>
                        <thead>
                            <tr>
                                <th>Shortcut</th>
                                <th>Description</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><kbd>Alt</kbd>+<kbd>↑</kbd></td>
                                <td>Scroll Up</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-arrow-up" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd"
                                                d="M8 15a.5.5 0 0 0 .5-.5V2.707l3.146 3.147a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V14.5a.5.5 0 0 0 .5.5z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><kbd>Alt</kbd>+<kbd>↓</kbd></td>
                                <td>Scroll Down</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-arrow-down" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd"
                                                d="M8 1a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L7.5 13.293V1.5A.5.5 0 0 1 8 1z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><kbd>Alt</kbd>+<kbd>[</kbd></td>
                                <td>Previous Media</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-skip-start" viewBox="0 0 16 16">
                                            <path
                                                d="M4 4a.5.5 0 0 1 1 0v3.248l6.267-3.636c.52-.302 1.233.043 1.233.696v7.384c0 .653-.713.998-1.233.696L5 8.752V12a.5.5 0 0 1-1 0V4zm7.5.633L5.696 8l5.804 3.367V4.633z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><kbd>Alt</kbd>+<kbd>]</kbd></td>
                                <td>Next Media</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-skip-end" viewBox="0 0 16 16">
                                            <path
                                                d="M12.5 4a.5.5 0 0 0-1 0v3.248L5.233 3.612C4.713 3.31 4 3.655 4 4.308v7.384c0 .653.713.998 1.233.696L11.5 8.752V12a.5.5 0 0 0 1 0V4zM5 4.633 10.804 8 5 11.367V4.633z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><kbd>Alt</kbd>+<kbd>\</kbd></td>
                                <td>Play / Pause Media</td>
                                <td>
                                    <div class="icon-container">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                            class="bi bi-play" viewBox="0 0 16 16">
                                            <path
                                                d="M10.804 8 5 4.633v6.734L10.804 8zm.792-.696a.802.802 0 0 1 0 1.392l-6.363 3.692C4.713 12.69 4 12.345 4 11.692V4.308c0-.653.713-.998 1.233-.696l6.363 3.692z" />
                                        </svg>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>

</html>