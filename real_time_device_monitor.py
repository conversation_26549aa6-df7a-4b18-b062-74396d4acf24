#!/usr/bin/env python3
"""
实时设备监控脚本
持续监控ADB设备连接状态，帮助诊断鸿蒙设备连接问题
"""

import subprocess
import time
import sys
from pathlib import Path
from datetime import datetime

def run_adb_command(command: list, timeout: int = 5) -> tuple[bool, str]:
    """执行ADB命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd=Path(__file__).parent
        )
        return result.returncode == 0, result.stdout + result.stderr
    except subprocess.TimeoutExpired:
        return False, "命令超时"
    except Exception as e:
        return False, f"执行错误: {str(e)}"

def get_current_time():
    """获取当前时间字符串"""
    return datetime.now().strftime("%H:%M:%S")

def check_devices():
    """检查当前连接的设备"""
    success, output = run_adb_command([r".\adb-bin\adb.exe", "devices", "-l"])
    if success:
        lines = output.strip().split('\n')
        devices = []
        for line in lines:
            if line and not line.startswith('List of devices') and '\t' in line:
                devices.append(line.strip())
        return devices
    return []

def monitor_devices():
    """持续监控设备连接状态"""
    print("🚀 开始实时监控ADB设备连接")
    print("=" * 50)
    print("📋 操作指南:")
    print("1. 确保鸿蒙平板已启用USB调试")
    print("2. 用USB线连接平板到电脑")
    print("3. 在平板上授权USB调试")
    print("4. 观察下方的实时监控信息")
    print("5. 按 Ctrl+C 停止监控")
    print("=" * 50)
    
    last_devices = []
    check_count = 0
    
    try:
        while True:
            check_count += 1
            current_time = get_current_time()
            
            # 检查设备
            devices = check_devices()
            
            # 如果设备状态发生变化
            if devices != last_devices:
                if devices:
                    print(f"\n🎉 [{current_time}] 检测到设备变化!")
                    for i, device in enumerate(devices, 1):
                        parts = device.split('\t')
                        device_id = parts[0]
                        status = parts[1] if len(parts) > 1 else "unknown"
                        print(f"   📱 设备 {i}: {device_id}")
                        print(f"      状态: {status}")
                        
                        # 如果设备已连接，获取详细信息
                        if status == "device":
                            print(f"      🔍 获取设备信息...")
                            get_device_info(device_id)
                else:
                    if last_devices:  # 之前有设备，现在没有了
                        print(f"\n⚠️  [{current_time}] 设备已断开连接")
                    else:
                        print(f"📡 [{current_time}] 等待设备连接... (检查次数: {check_count})")
                
                last_devices = devices.copy()
            else:
                # 每10次检查显示一次状态
                if check_count % 10 == 0:
                    if devices:
                        print(f"✅ [{current_time}] 设备连接正常 (已检查 {check_count} 次)")
                    else:
                        print(f"⏳ [{current_time}] 继续等待设备... (已检查 {check_count} 次)")
            
            time.sleep(2)  # 每2秒检查一次
            
    except KeyboardInterrupt:
        print(f"\n\n🛑 监控已停止 (共检查了 {check_count} 次)")
        if devices:
            print("✅ 最终状态: 有设备连接")
            return True
        else:
            print("❌ 最终状态: 无设备连接")
            return False

def get_device_info(device_id: str):
    """获取设备详细信息"""
    cmd_base = [r".\adb-bin\adb.exe", "-s", device_id]
    
    # 获取基本信息
    properties = [
        ("ro.product.model", "型号"),
        ("ro.product.brand", "品牌"),
        ("ro.build.version.release", "系统版本"),
        ("ro.harmonyos.version.name", "鸿蒙版本"),
    ]
    
    for prop, desc in properties:
        success, output = run_adb_command(cmd_base + ["shell", "getprop", prop], timeout=3)
        if success and output.strip():
            print(f"      📋 {desc}: {output.strip()}")

def test_connection_after_detection():
    """设备检测到后进行连接测试"""
    print("\n🧪 开始连接功能测试...")
    
    devices = check_devices()
    if not devices:
        print("❌ 没有检测到设备")
        return False
    
    device_id = devices[0].split('\t')[0]
    print(f"🎯 测试设备: {device_id}")
    
    cmd_base = [r".\adb-bin\adb.exe", "-s", device_id]
    
    # 测试基本连接
    print("   测试基本shell连接...")
    success, output = run_adb_command(cmd_base + ["shell", "echo", "hello"], timeout=5)
    if success:
        print("   ✅ Shell连接正常")
    else:
        print(f"   ❌ Shell连接失败: {output}")
        return False
    
    # 测试input命令
    print("   测试input命令可用性...")
    success, output = run_adb_command(cmd_base + ["shell", "input"], timeout=5)
    if success:
        print("   ✅ input命令可用")
        
        # 测试Home键（安全测试）
        print("   测试Home键输入...")
        success, output = run_adb_command(cmd_base + ["shell", "input", "keyevent", "KEYCODE_HOME"], timeout=5)
        if success:
            print("   ✅ 键盘输入测试成功")
        else:
            print(f"   ⚠️  键盘输入测试失败: {output}")
    else:
        print(f"   ❌ input命令不可用: {output}")
    
    print("\n🎉 设备兼容性测试完成!")
    print("💡 现在可以尝试运行 InputShare 主程序:")
    print("   python main.py")
    
    return True

def main():
    """主函数"""
    print("🔍 鸿蒙设备实时连接监控")
    print("版本: 2.0 - 增强版")
    print()
    
    # 首先检查当前状态
    devices = check_devices()
    if devices:
        print("✅ 检测到已连接的设备:")
        for device in devices:
            print(f"   📱 {device}")
        
        choice = input("\n是否直接进行连接测试? (y/n): ").strip().lower()
        if choice == 'y':
            test_connection_after_detection()
            return
    
    # 开始实时监控
    device_detected = monitor_devices()
    
    # 如果检测到设备，进行连接测试
    if device_detected:
        choice = input("\n是否进行连接功能测试? (y/n): ").strip().lower()
        if choice == 'y':
            test_connection_after_detection()

if __name__ == "__main__":
    main()
