#!/usr/bin/env python3
"""
鸿蒙系统兼容性测试脚本
测试InputShare与鸿蒙4.0.2的兼容性
"""

import subprocess
import sys
import time
from pathlib import Path

def run_adb_command(command: list, timeout: int = 10) -> tuple[bool, str]:
    """执行ADB命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd=Path(__file__).parent
        )
        return result.returncode == 0, result.stdout + result.stderr
    except subprocess.TimeoutExpired:
        return False, "命令超时"
    except Exception as e:
        return False, f"执行错误: {str(e)}"

def test_adb_server():
    """测试ADB服务器"""
    print("🔧 测试ADB服务器...")
    
    # 启动ADB服务器
    success, output = run_adb_command([r".\adb-bin\adb.exe", "start-server"])
    if success:
        print("✅ ADB服务器启动成功")
    else:
        print(f"❌ ADB服务器启动失败: {output}")
        return False
    
    # 检查ADB版本
    success, output = run_adb_command([r".\adb-bin\adb.exe", "version"])
    if success:
        print(f"✅ ADB版本信息: {output.strip()}")
    else:
        print(f"❌ 无法获取ADB版本: {output}")
    
    return True

def test_device_detection():
    """测试设备检测"""
    print("\n📱 测试设备检测...")
    
    success, output = run_adb_command([r".\adb-bin\adb.exe", "devices", "-l"])
    if success:
        lines = output.strip().split('\n')
        devices = [line for line in lines if line and not line.startswith('List of devices')]
        
        if devices:
            print(f"✅ 检测到 {len(devices)} 个设备:")
            for device in devices:
                print(f"   📱 {device}")
            return True
        else:
            print("⚠️  未检测到任何设备")
            return False
    else:
        print(f"❌ 设备检测失败: {output}")
        return False

def test_device_properties(device_id: str = None):
    """测试设备属性"""
    print(f"\n🔍 测试设备属性...")
    
    cmd = [r".\adb-bin\adb.exe"]
    if device_id:
        cmd.extend(["-s", device_id])
    
    # 测试基本属性
    properties = [
        ("ro.build.version.release", "Android版本"),
        ("ro.product.model", "设备型号"),
        ("ro.product.brand", "设备品牌"),
        ("ro.build.version.sdk", "SDK版本"),
        ("ro.harmonyos.version.name", "鸿蒙版本"),  # 鸿蒙特有属性
    ]
    
    for prop, desc in properties:
        success, output = run_adb_command(cmd + ["shell", "getprop", prop])
        if success and output.strip():
            print(f"✅ {desc}: {output.strip()}")
        else:
            print(f"⚠️  {desc}: 无法获取")

def test_input_capabilities(device_id: str = None):
    """测试输入能力"""
    print(f"\n⌨️  测试输入能力...")
    
    cmd = [r".\adb-bin\adb.exe"]
    if device_id:
        cmd.extend(["-s", device_id])
    
    # 测试input命令是否可用
    success, output = run_adb_command(cmd + ["shell", "input"])
    if success:
        print("✅ input命令可用")
        print(f"   支持的输入类型: {output.strip()}")
    else:
        print(f"❌ input命令不可用: {output}")
        return False
    
    # 测试简单的输入命令
    print("🧪 测试简单输入命令...")
    test_commands = [
        (["shell", "input", "keyevent", "KEYCODE_HOME"], "Home键测试"),
        (["shell", "input", "tap", "100", "100"], "触摸测试"),
    ]
    
    for cmd_args, desc in test_commands:
        print(f"   测试: {desc}")
        success, output = run_adb_command(cmd + cmd_args, timeout=5)
        if success:
            print(f"   ✅ {desc} 成功")
        else:
            print(f"   ❌ {desc} 失败: {output}")
    
    return True

def test_scrcpy_compatibility(device_id: str = None):
    """测试scrcpy兼容性"""
    print(f"\n🖥️  测试scrcpy兼容性...")
    
    cmd = [r".\adb-bin\adb.exe"]
    if device_id:
        cmd.extend(["-s", device_id])
    
    # 检查是否可以推送scrcpy服务器
    server_path = Path("server/scrcpy-server")
    if not server_path.exists():
        print("❌ scrcpy-server文件不存在")
        return False
    
    # 尝试推送服务器文件
    success, output = run_adb_command(
        cmd + ["push", str(server_path), "/data/local/tmp/scrcpy-server.jar"]
    )
    if success:
        print("✅ scrcpy服务器推送成功")
    else:
        print(f"❌ scrcpy服务器推送失败: {output}")
        return False
    
    # 测试服务器是否可以执行
    success, output = run_adb_command(
        cmd + ["shell", "ls", "-l", "/data/local/tmp/scrcpy-server.jar"]
    )
    if success:
        print("✅ scrcpy服务器文件存在")
    else:
        print(f"❌ scrcpy服务器文件检查失败: {output}")
    
    return True

def main():
    """主测试函数"""
    print("🚀 鸿蒙4.0.2 与 InputShare 兼容性测试")
    print("=" * 50)
    
    # 测试ADB服务器
    if not test_adb_server():
        print("\n❌ ADB服务器测试失败，无法继续")
        return
    
    # 测试设备检测
    has_device = test_device_detection()
    
    if not has_device:
        print("\n⚠️  未检测到设备，请确保:")
        print("   1. 设备已连接并启用USB调试")
        print("   2. 已授权此计算机进行调试")
        print("   3. 设备驱动已正确安装")
        return
    
    # 获取第一个设备ID
    success, output = run_adb_command([r".\adb-bin\adb.exe", "devices"])
    if success:
        lines = output.strip().split('\n')[1:]  # 跳过标题行
        if lines:
            device_id = lines[0].split('\t')[0]
            print(f"\n🎯 使用设备: {device_id}")
            
            # 测试设备属性
            test_device_properties(device_id)
            
            # 测试输入能力
            test_input_capabilities(device_id)
            
            # 测试scrcpy兼容性
            test_scrcpy_compatibility(device_id)
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")
    print("\n💡 如果所有测试都通过，您的鸿蒙设备应该可以与InputShare兼容")
    print("   如果有测试失败，可能需要特殊配置或鸿蒙系统限制")

if __name__ == "__main__":
    main()
