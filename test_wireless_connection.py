#!/usr/bin/env python3
"""
无线ADB连接测试脚本
用于测试鸿蒙设备的无线调试连接
"""

import subprocess
import sys
import time
from pathlib import Path

def run_adb_command(command: list, timeout: int = 10) -> tuple[bool, str]:
    """执行ADB命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd=Path(__file__).parent
        )
        return result.returncode == 0, result.stdout + result.stderr
    except subprocess.TimeoutExpired:
        return False, "命令超时"
    except Exception as e:
        return False, f"执行错误: {str(e)}"

def test_wireless_pairing(ip_port: str, pairing_code: str):
    """测试无线配对"""
    print(f"🔗 尝试配对设备: {ip_port}")
    print(f"📱 配对码: {pairing_code}")
    
    cmd = [r".\adb-bin\adb.exe", "pair", ip_port, pairing_code]
    success, output = run_adb_command(cmd, timeout=30)
    
    if success:
        print("✅ 设备配对成功!")
        print(f"   输出: {output.strip()}")
        return True
    else:
        print(f"❌ 设备配对失败: {output}")
        return False

def test_wireless_connect(ip_port: str):
    """测试无线连接"""
    print(f"🔌 尝试连接设备: {ip_port}")
    
    cmd = [r".\adb-bin\adb.exe", "connect", ip_port]
    success, output = run_adb_command(cmd, timeout=20)
    
    if success:
        print("✅ 设备连接成功!")
        print(f"   输出: {output.strip()}")
        return True
    else:
        print(f"❌ 设备连接失败: {output}")
        return False

def check_connected_devices():
    """检查已连接的设备"""
    print("📱 检查已连接的设备...")
    
    success, output = run_adb_command([r".\adb-bin\adb.exe", "devices", "-l"])
    if success:
        lines = output.strip().split('\n')
        devices = [line for line in lines if line and not line.startswith('List of devices')]
        
        if devices:
            print(f"✅ 发现 {len(devices)} 个设备:")
            for device in devices:
                print(f"   📱 {device}")
            return True
        else:
            print("⚠️  未发现任何设备")
            return False
    else:
        print(f"❌ 设备检查失败: {output}")
        return False

def test_device_info(device_id: str = None):
    """测试设备信息"""
    print("🔍 获取设备信息...")
    
    cmd = [r".\adb-bin\adb.exe"]
    if device_id:
        cmd.extend(["-s", device_id])
    
    # 获取设备基本信息
    properties = [
        ("ro.product.model", "设备型号"),
        ("ro.product.brand", "设备品牌"),
        ("ro.build.version.release", "系统版本"),
        ("ro.harmonyos.version.name", "鸿蒙版本"),
    ]
    
    for prop, desc in properties:
        success, output = run_adb_command(cmd + ["shell", "getprop", prop], timeout=5)
        if success and output.strip():
            print(f"   {desc}: {output.strip()}")

def main():
    """主函数"""
    print("🚀 鸿蒙无线调试连接测试")
    print("=" * 40)
    
    # 首先检查是否有已连接的设备
    if check_connected_devices():
        print("\n✅ 检测到已连接的设备，跳过配对步骤")
        success, output = run_adb_command([r".\adb-bin\adb.exe", "devices"])
        if success:
            lines = output.strip().split('\n')[1:]
            if lines:
                device_id = lines[0].split('\t')[0]
                test_device_info(device_id)
        return
    
    print("\n📋 请按照以下步骤操作:")
    print("1. 在鸿蒙设备上进入: 设置 → 系统和更新 → 开发人员选项 → 无线调试")
    print("2. 确保无线调试已开启")
    print("3. 查看显示的IP地址和端口号")
    print()
    
    # 获取用户输入
    choice = input("请选择操作:\n1. 首次配对设备\n2. 直接连接已配对设备\n请输入选择 (1/2): ").strip()
    
    if choice == "1":
        print("\n📱 首次配对设备")
        print("请在鸿蒙设备上点击'使用配对码配对设备'")
        
        pairing_ip = input("请输入配对IP地址和端口 (例如: *************:37829): ").strip()
        pairing_code = input("请输入6位配对码: ").strip()
        
        if not pairing_ip or not pairing_code:
            print("❌ 输入信息不完整")
            return
        
        # 尝试配对
        if test_wireless_pairing(pairing_ip, pairing_code):
            print("\n🎉 配对成功! 现在尝试连接...")
            
            # 配对成功后，通常使用主要的IP:5555端口连接
            main_ip = pairing_ip.split(':')[0] + ":5555"
            if test_wireless_connect(main_ip):
                check_connected_devices()
                test_device_info()
        
    elif choice == "2":
        print("\n🔌 直接连接已配对设备")
        
        device_ip = input("请输入设备IP地址和端口 (例如: *************:5555): ").strip()
        
        if not device_ip:
            print("❌ 请输入有效的IP地址和端口")
            return
        
        # 尝试连接
        if test_wireless_connect(device_ip):
            check_connected_devices()
            test_device_info()
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
