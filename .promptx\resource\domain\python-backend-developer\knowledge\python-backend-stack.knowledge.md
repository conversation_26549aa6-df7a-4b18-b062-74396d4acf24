# Python后端技术栈知识体系

## 🐍 Python语言核心

### Python版本特性
- **Python 3.11+**：性能提升、更好的错误信息、异常组
- **Python 3.10**：模式匹配、联合类型操作符
- **Python 3.9**：字典合并操作符、类型提示改进
- **Python 3.8**：海象操作符、位置参数、TypedDict

### 核心语言特性
```python
# 现代Python特性示例
from typing import TypedDict, Optional, Union
from dataclasses import dataclass
from enum import Enum

class UserStatus(Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"

@dataclass
class User:
    id: int
    name: str
    status: UserStatus
    email: Optional[str] = None

# 模式匹配 (Python 3.10+)
def process_user(user: User) -> str:
    match user.status:
        case UserStatus.ACTIVE:
            return f"Active user: {user.name}"
        case UserStatus.INACTIVE:
            return f"Inactive user: {user.name}"
```

## 🌐 Web框架生态

### FastAPI (推荐现代框架)
```python
from fastapi import FastAPI, Depends, HTTPException
from pydantic import BaseModel
from typing import List

app = FastAPI(title="API服务", version="1.0.0")

class UserCreate(BaseModel):
    username: str
    email: str

@app.post("/users/", response_model=User)
async def create_user(user: UserCreate):
    # 异步处理逻辑
    return await user_service.create(user)

# 自动生成OpenAPI文档
# 内置数据验证和序列化
# 原生异步支持
```

### Django (全栈框架)
```python
# Django REST Framework
from rest_framework import serializers, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email']

class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    
    @action(detail=True, methods=['post'])
    def set_password(self, request, pk=None):
        # 自定义动作
        pass
```

### Flask (轻量框架)
```python
from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from marshmallow import Schema, fields

app = Flask(__name__)
db = SQLAlchemy(app)

class UserSchema(Schema):
    id = fields.Int()
    username = fields.Str(required=True)
    email = fields.Email(required=True)

@app.route('/users', methods=['POST'])
def create_user():
    schema = UserSchema()
    try:
        user_data = schema.load(request.json)
        # 处理逻辑
        return jsonify(schema.dump(user)), 201
    except ValidationError as err:
        return jsonify(err.messages), 400
```

## 🗄️ 数据库技术栈

### SQLAlchemy (ORM)
```python
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine

Base = declarative_base()

class User(Base):
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系定义
    posts = relationship("Post", back_populates="author")

# 异步数据库操作
async def get_user(session: AsyncSession, user_id: int):
    result = await session.get(User, user_id)
    return result
```

### 数据库迁移 (Alembic)
```python
# alembic/versions/001_create_users_table.py
from alembic import op
import sqlalchemy as sa

def upgrade():
    op.create_table(
        'users',
        sa.Column('id', sa.Integer, primary_key=True),
        sa.Column('username', sa.String(50), nullable=False),
        sa.Column('email', sa.String(100), nullable=False),
        sa.Column('created_at', sa.DateTime, nullable=False)
    )
    op.create_index('ix_users_username', 'users', ['username'])

def downgrade():
    op.drop_table('users')
```

### Redis缓存
```python
import redis.asyncio as redis
from typing import Optional
import json

class CacheService:
    def __init__(self, redis_url: str):
        self.redis = redis.from_url(redis_url)
    
    async def get(self, key: str) -> Optional[dict]:
        value = await self.redis.get(key)
        return json.loads(value) if value else None
    
    async def set(self, key: str, value: dict, expire: int = 3600):
        await self.redis.setex(key, expire, json.dumps(value))
    
    async def delete(self, key: str):
        await self.redis.delete(key)
```

## ⚡ 异步编程

### asyncio核心概念
```python
import asyncio
import aiohttp
from typing import List

async def fetch_data(session: aiohttp.ClientSession, url: str) -> dict:
    async with session.get(url) as response:
        return await response.json()

async def fetch_multiple(urls: List[str]) -> List[dict]:
    async with aiohttp.ClientSession() as session:
        tasks = [fetch_data(session, url) for url in urls]
        results = await asyncio.gather(*tasks)
        return results

# 异步上下文管理器
class AsyncDatabaseConnection:
    async def __aenter__(self):
        self.connection = await create_connection()
        return self.connection
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.connection.close()
```

### Celery任务队列
```python
from celery import Celery
from celery.result import AsyncResult

app = Celery('tasks', broker='redis://localhost:6379')

@app.task(bind=True, max_retries=3)
def process_data(self, data: dict):
    try:
        # 长时间运行的任务
        result = heavy_computation(data)
        return result
    except Exception as exc:
        # 重试机制
        raise self.retry(exc=exc, countdown=60)

# 任务调用
task = process_data.delay({"key": "value"})
result = task.get(timeout=10)
```

## 🔒 安全与认证

### JWT认证
```python
import jwt
from datetime import datetime, timedelta
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class AuthService:
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
    
    def create_access_token(self, data: dict, expires_delta: timedelta = None):
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=15)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm="HS256")
        return encoded_jwt
    
    def verify_token(self, token: str):
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=["HS256"])
            return payload
        except jwt.PyJWTError:
            return None
    
    def hash_password(self, password: str) -> str:
        return pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        return pwd_context.verify(plain_password, hashed_password)
```

### 数据验证 (Pydantic)
```python
from pydantic import BaseModel, validator, Field
from typing import Optional
import re

class UserCreate(BaseModel):
    username: str = Field(..., min_length=3, max_length=50)
    email: str = Field(..., regex=r'^[\w\.-]+@[\w\.-]+\.\w+$')
    password: str = Field(..., min_length=8)
    age: Optional[int] = Field(None, ge=0, le=120)
    
    @validator('username')
    def username_alphanumeric(cls, v):
        assert v.isalnum(), '用户名只能包含字母和数字'
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if not re.search(r'[A-Z]', v):
            raise ValueError('密码必须包含大写字母')
        if not re.search(r'[a-z]', v):
            raise ValueError('密码必须包含小写字母')
        if not re.search(r'\d', v):
            raise ValueError('密码必须包含数字')
        return v
```

## 🧪 测试框架

### pytest测试
```python
import pytest
import asyncio
from httpx import AsyncClient
from unittest.mock import AsyncMock, patch

@pytest.fixture
async def async_client():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac

@pytest.mark.asyncio
async def test_create_user(async_client: AsyncClient):
    response = await async_client.post("/users/", json={
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "TestPass123"
    })
    assert response.status_code == 201
    assert response.json()["username"] == "testuser"

# 参数化测试
@pytest.mark.parametrize("username,email,expected_status", [
    ("valid_user", "<EMAIL>", 201),
    ("", "<EMAIL>", 422),
    ("valid_user", "invalid_email", 422),
])
async def test_user_creation_validation(async_client, username, email, expected_status):
    response = await async_client.post("/users/", json={
        "username": username,
        "email": email,
        "password": "TestPass123"
    })
    assert response.status_code == expected_status
```

## 📊 监控与日志

### 结构化日志
```python
import structlog
import logging.config

# 配置结构化日志
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# 使用示例
async def process_user_request(user_id: int, action: str):
    log = logger.bind(user_id=user_id, action=action)
    log.info("开始处理用户请求")
    
    try:
        result = await perform_action(user_id, action)
        log.info("请求处理成功", result=result)
        return result
    except Exception as e:
        log.error("请求处理失败", error=str(e))
        raise
```

### 性能监控
```python
from prometheus_client import Counter, Histogram, generate_latest
import time

# 指标定义
REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')

# 中间件
@app.middleware("http")
async def metrics_middleware(request: Request, call_next):
    start_time = time.time()
    
    response = await call_next(request)
    
    duration = time.time() - start_time
    REQUEST_COUNT.labels(method=request.method, endpoint=request.url.path).inc()
    REQUEST_DURATION.observe(duration)
    
    return response

@app.get("/metrics")
async def metrics():
    return Response(generate_latest(), media_type="text/plain")
```

## 🐳 部署与DevOps

### Docker容器化
```dockerfile
# 多阶段构建
FROM python:3.11-slim as builder

WORKDIR /app
COPY requirements.txt .
RUN pip install --user --no-cache-dir -r requirements.txt

FROM python:3.11-slim

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app

WORKDIR /app
COPY --from=builder /root/.local /home/<USER>/.local
COPY . .

# 设置环境变量
ENV PATH=/home/<USER>/.local/bin:$PATH
ENV PYTHONPATH=/app

USER app
EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 配置管理
```python
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # 应用配置
    app_name: str = "Python Backend API"
    debug: bool = False
    
    # 数据库配置
    database_url: str
    database_pool_size: int = 10
    
    # Redis配置
    redis_url: str = "redis://localhost:6379"
    
    # 安全配置
    secret_key: str
    access_token_expire_minutes: int = 30
    
    # 第三方服务
    email_service_api_key: Optional[str] = None
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

settings = Settings()
```

这个知识体系涵盖了Python后端开发的核心技术栈，从语言特性到部署运维，为开发高质量的Python后端应用提供了全面的技术支持。

## 🔧 开发工具链

### 包管理
- **Poetry**: 现代依赖管理和打包工具
- **pip-tools**: 依赖锁定和管理
- **pipenv**: 虚拟环境和依赖管理

### 代码质量工具
- **black**: 代码格式化
- **isort**: 导入排序
- **mypy**: 静态类型检查
- **flake8**: 代码风格检查
- **pylint**: 代码质量分析

### 性能分析
- **cProfile**: 性能分析
- **py-spy**: 生产环境性能监控
- **memory_profiler**: 内存使用分析
