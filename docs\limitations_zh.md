# 已知缺陷

## 设备熄屏后会自动断开连接

在程序成功连接后，无论是电脑端还是安卓端熄屏，都会导致连接断开。此时需要重新启动程序并重新连接才能继续使用。

**解决办法**：目前程序内提供了“保持设备屏幕常亮”设置，开启后可以防止安卓端设备熄屏，从而避免此问题。

## 我在共享键鼠后，安卓设备上的鼠标飘忽不定，如何解决？

你可以尝试通过你的鼠标驱动（如罗技的 G Hub、雷蛇的雷云等）设置你的鼠标的 __“回报率”__（一些地方叫作 __”轮询率“__）到 __125Hz__ 或者相近的数值，情况应该会有很大改善。

## 与其它使用 ADB 的程序冲突

如果你在使用其它同样依赖于 ADB 连接安卓设备的电脑软件（如 Android Studio）时使用本软件，你可能会遇到本软件无法成功连接设备的情况。类似地，你在使用本软件连接的安卓设备也无法被其它使用 ADB 的软件连接。
这是 ADB 本身的限制，目前没有解决办法。

## 与 Bonjour 冲突

一些同类软件，如 [barrier](https://github.com/debauchee/barrier) 和 [deskflow](https://github.com/deskflow/deskflow)，会使用 [Bonjour](https://developer.apple.com/bonjour/) 来简化网络连接。
但根据部分用户反馈，在电脑上运行 barrier 并启动 Bonjour 后，再使用本软件时，会导致本软件的连接成功率显著降低。

**解决办法**：目前本软件与 Bonjour 的冲突原因尚不清楚。你可以尝试先使用本软件连接，再启动 barrier 来规避此问题。
