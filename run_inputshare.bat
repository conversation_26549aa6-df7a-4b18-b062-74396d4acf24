@echo off
chcp 65001 >nul
title InputShare 启动器

echo ========================================
echo       InputShare 一键启动器
echo ========================================
echo.

echo [INFO] 正在启动 InputShare...
echo [INFO] 项目路径: %~dp0
echo.

REM 切换到脚本所在目录
cd /d "%~dp0"

REM 运行main.py
python main.py

REM 如果程序异常退出，显示错误信息
if %ERRORLEVEL% neq 0 (
    echo.
    echo [ERROR] 程序异常退出，错误代码: %ERRORLEVEL%
    echo [INFO] 请检查：
    echo   1. Python 是否已正确安装
    echo   2. 依赖包是否已安装 (pip install -r requirements.txt)
    echo   3. 设备连接是否正常
    echo.
    pause
) else (
    echo.
    echo [INFO] 程序正常退出
)
