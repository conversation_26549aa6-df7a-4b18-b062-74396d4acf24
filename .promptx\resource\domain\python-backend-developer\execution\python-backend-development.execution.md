<execution>
  <constraint>
    ## 客观技术限制
    - **Python版本兼容性**：必须考虑目标Python版本的特性支持
    - **GIL限制**：Python全局解释器锁对CPU密集型任务的影响
    - **内存管理**：Python的垃圾回收机制和内存使用特点
    - **包依赖管理**：第三方包的版本兼容性和安全性
    - **部署环境约束**：目标服务器的系统环境和资源限制
    - **性能边界**：Python在高并发场景下的性能瓶颈
  </constraint>

  <rule>
    ## 强制性开发规则
    - **代码规范强制**：必须遵循PEP 8代码风格规范
    - **类型提示必需**：所有函数和方法必须添加类型注解
    - **异常处理强制**：所有可能出错的地方必须有适当的异常处理
    - **测试覆盖要求**：核心业务逻辑必须有单元测试覆盖
    - **安全验证强制**：所有用户输入必须进行验证和清理
    - **日志记录必需**：关键操作和错误必须记录日志
    - **文档完整性**：API接口必须有完整的文档说明
  </rule>

  <guideline>
    ## 开发指导原则
    - **Pythonic编程**：优先使用Python惯用法和最佳实践
    - **简洁优雅**：代码应该简洁明了，易于理解和维护
    - **性能意识**：在保证可读性的前提下考虑性能优化
    - **安全第一**：始终将安全性作为首要考虑因素
    - **可测试性**：编写易于测试的代码，支持单元测试
    - **可扩展性**：设计时考虑未来的功能扩展需求
    - **错误友好**：提供清晰的错误信息和处理机制
  </guideline>

  <process>
    ## Python后端开发流程

    ### Phase 1: 项目初始化
    1. **环境准备**：
       ```bash
       # 创建虚拟环境
       python -m venv venv
       source venv/bin/activate  # Linux/Mac
       # venv\Scripts\activate  # Windows
       
       # 安装基础工具
       pip install --upgrade pip
       pip install black isort mypy pytest
       ```

    2. **项目结构设计**：
       ```
       project/
       ├── app/
       │   ├── __init__.py
       │   ├── main.py          # 应用入口
       │   ├── models/          # 数据模型
       │   ├── schemas/         # Pydantic模型
       │   ├── api/             # API路由
       │   ├── services/        # 业务逻辑
       │   ├── utils/           # 工具函数
       │   └── config.py        # 配置管理
       ├── tests/               # 测试文件
       ├── requirements.txt     # 依赖管理
       ├── Dockerfile          # 容器化
       └── README.md           # 项目文档
       ```

    3. **依赖管理**：
       ```python
       # requirements.txt 示例
       fastapi==0.104.1
       uvicorn[standard]==0.24.0
       sqlalchemy==2.0.23
       alembic==1.12.1
       pydantic==2.5.0
       python-multipart==0.0.6
       ```

    ### Phase 2: 核心开发
    1. **数据模型设计**：
       ```python
       # models/user.py
       from sqlalchemy import Column, Integer, String, DateTime
       from sqlalchemy.ext.declarative import declarative_base
       from datetime import datetime

       Base = declarative_base()

       class User(Base):
           __tablename__ = "users"
           
           id: int = Column(Integer, primary_key=True, index=True)
           username: str = Column(String(50), unique=True, index=True)
           email: str = Column(String(100), unique=True, index=True)
           created_at: datetime = Column(DateTime, default=datetime.utcnow)
       ```

    2. **API接口开发**：
       ```python
       # api/users.py
       from fastapi import APIRouter, Depends, HTTPException
       from typing import List
       
       router = APIRouter(prefix="/users", tags=["users"])
       
       @router.post("/", response_model=UserResponse)
       async def create_user(
           user: UserCreate,
           db: Session = Depends(get_db)
       ) -> UserResponse:
           """创建新用户"""
           # 业务逻辑实现
           pass
       ```

    3. **业务逻辑层**：
       ```python
       # services/user_service.py
       from typing import Optional, List
       from sqlalchemy.orm import Session
       
       class UserService:
           def __init__(self, db: Session):
               self.db = db
           
           async def create_user(self, user_data: UserCreate) -> User:
               """创建用户业务逻辑"""
               # 验证、处理、保存
               pass
       ```

    ### Phase 3: 质量保证
    1. **代码质量检查**：
       ```bash
       # 代码格式化
       black app/
       isort app/
       
       # 类型检查
       mypy app/
       
       # 代码质量检查
       flake8 app/
       ```

    2. **测试编写**：
       ```python
       # tests/test_users.py
       import pytest
       from fastapi.testclient import TestClient
       
       def test_create_user(client: TestClient):
           response = client.post("/users/", json={
               "username": "testuser",
               "email": "<EMAIL>"
           })
           assert response.status_code == 201
           assert response.json()["username"] == "testuser"
       ```

    3. **性能测试**：
       ```python
       # 使用pytest-benchmark
       def test_user_creation_performance(benchmark):
           result = benchmark(create_user_function, user_data)
           assert result is not None
       ```

    ### Phase 4: 部署上线
    1. **容器化**：
       ```dockerfile
       FROM python:3.11-slim
       
       WORKDIR /app
       COPY requirements.txt .
       RUN pip install --no-cache-dir -r requirements.txt
       
       COPY . .
       EXPOSE 8000
       CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
       ```

    2. **环境配置**：
       ```python
       # config.py
       from pydantic_settings import BaseSettings
       
       class Settings(BaseSettings):
           database_url: str
           secret_key: str
           debug: bool = False
           
           class Config:
               env_file = ".env"
       ```

    3. **监控和日志**：
       ```python
       import logging
       from fastapi import Request
       
       # 配置日志
       logging.basicConfig(
           level=logging.INFO,
           format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
       )
       
       # 请求日志中间件
       @app.middleware("http")
       async def log_requests(request: Request, call_next):
           start_time = time.time()
           response = await call_next(request)
           process_time = time.time() - start_time
           logger.info(f"{request.method} {request.url} - {response.status_code} - {process_time:.3f}s")
           return response
       ```
  </process>

  <criteria>
    ## 开发质量评价标准

    ### 代码质量
    - ✅ 遵循PEP 8代码规范
    - ✅ 完整的类型注解覆盖
    - ✅ 适当的注释和文档字符串
    - ✅ 合理的函数和类设计
    - ✅ 无明显的代码异味

    ### 功能完整性
    - ✅ 核心业务逻辑正确实现
    - ✅ 完整的错误处理机制
    - ✅ 适当的数据验证
    - ✅ 安全性考虑充分
    - ✅ API接口设计合理

    ### 测试覆盖
    - ✅ 单元测试覆盖率 ≥ 80%
    - ✅ 集成测试覆盖关键流程
    - ✅ 性能测试验证关键指标
    - ✅ 安全测试覆盖敏感操作
    - ✅ 测试用例设计合理

    ### 部署就绪
    - ✅ 容器化配置正确
    - ✅ 环境配置管理完善
    - ✅ 日志和监控配置
    - ✅ 文档完整清晰
    - ✅ CI/CD流水线配置
  </criteria>
</execution>
