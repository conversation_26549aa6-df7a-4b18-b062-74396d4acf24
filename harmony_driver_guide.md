# 鸿蒙设备驱动安装指南

## 🔧 解决鸿蒙设备ADB连接问题

### 问题现象
- ADB无法检测到鸿蒙设备
- 设备管理器中设备显示为未知设备
- USB调试已启用但无法连接

### 解决方案

#### 方案1：安装华为官方驱动
1. **下载华为手机助手 (HiSuite)**
   - 官网：https://consumer.huawei.com/cn/support/hisuite/
   - 安装后会自动安装USB驱动

2. **或下载华为USB驱动**
   - 搜索"华为USB驱动下载"
   - 安装对应的驱动程序

#### 方案2：手动安装ADB驱动
1. **打开设备管理器**
   - Win+X → 设备管理器
   - 查找带黄色感叹号的设备

2. **更新驱动程序**
   - 右键未知设备 → 更新驱动程序
   - 选择"浏览我的电脑以查找驱动程序"
   - 选择"让我从计算机上的可用驱动程序列表中选取"
   - 选择"Android Device" → "Android ADB Interface"

#### 方案3：使用通用ADB驱动
1. **下载通用ADB驱动**
   - Google USB Driver
   - Universal ADB Drivers

2. **强制安装驱动**
   - 设备管理器中右键设备
   - 更新驱动程序 → 手动选择驱动

### 鸿蒙特殊设置

#### 开发者选项中的关键设置
- ✅ USB调试
- ✅ "仅充电"模式下允许ADB调试  
- ✅ USB安装
- ✅ USB调试（安全设置）
- ✅ 撤销USB调试授权（清除后重新授权）

#### USB连接模式
连接USB后在通知栏选择：
- 📁 传输文件 (MTP)
- 🔧 开发者模式
- ❌ 不要选择"仅充电"

### 测试连接
```bash
# 在InputShare目录中运行
.\adb-bin\adb.exe devices -l

# 应该显示类似：
# List of devices attached
# ABC123456789    device usb:1-1 product:xxx model:xxx device:xxx
```

### 如果仍然无法连接

#### 尝试无线连接
1. 通过USB先建立连接
2. 启用TCP/IP模式：
   ```bash
   .\adb-bin\adb.exe tcpip 5555
   ```
3. 断开USB，通过WiFi连接：
   ```bash
   .\adb-bin\adb.exe connect 设备IP:5555
   ```

#### 使用华为开发工具
- 下载华为DevEco Studio
- 使用HDC（华为设备连接器）替代ADB
- 可能有更好的鸿蒙兼容性

### 常见错误及解决

#### "device unauthorized"
- 在设备上重新授权USB调试
- 撤销所有USB调试授权后重新连接

#### "device offline"  
- 重启ADB服务器：
  ```bash
  .\adb-bin\adb.exe kill-server
  .\adb-bin\adb.exe start-server
  ```

#### 驱动问题
- 卸载现有驱动
- 重新安装华为官方驱动
- 尝试不同的USB端口

### 验证成功连接
连接成功后应该能看到：
- 设备ID和状态
- 能执行基本ADB命令
- InputShare可以检测到设备
