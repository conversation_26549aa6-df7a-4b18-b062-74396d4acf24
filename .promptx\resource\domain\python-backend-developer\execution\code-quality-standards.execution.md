<execution>
  <constraint>
    ## 客观技术限制
    - **PEP 8规范固定**：Python官方代码风格指南不可违背
    - **类型系统限制**：Python的渐进式类型系统特性
    - **工具兼容性**：代码质量工具的版本和配置限制
    - **团队技能水平**：团队成员对代码质量工具的熟悉程度
    - **项目时间约束**：代码质量改进与项目进度的平衡
  </constraint>

  <rule>
    ## 强制性质量规则
    - **PEP 8合规性**：所有代码必须通过flake8或black检查
    - **类型注解强制**：所有公共函数和方法必须有类型注解
    - **文档字符串必需**：所有模块、类、函数必须有docstring
    - **测试覆盖要求**：新增代码必须有对应的单元测试
    - **安全扫描通过**：代码必须通过安全漏洞扫描
    - **代码审查必需**：所有代码变更必须经过同行评审
    - **静态分析通过**：必须通过mypy、pylint等静态分析工具
  </rule>

  <guideline>
    ## 质量改进指导
    - **渐进式改进**：逐步提升代码质量，避免一次性大改
    - **工具自动化**：使用pre-commit hooks自动化质量检查
    - **团队培训**：定期进行代码质量最佳实践培训
    - **质量度量**：建立代码质量指标和监控机制
    - **重构优先**：优先重构高风险和高维护成本的代码
    - **文档同步**：代码变更时同步更新相关文档
  </guideline>

  <process>
    ## 代码质量保证流程

    ### Phase 1: 开发环境配置
    1. **质量工具安装**：
       ```bash
       # 安装代码质量工具
       pip install black isort mypy flake8 pylint pytest pytest-cov
       pip install pre-commit bandit safety
       ```

    2. **配置文件设置**：
       ```toml
       # pyproject.toml
       [tool.black]
       line-length = 88
       target-version = ['py311']
       include = '\.pyi?$'
       
       [tool.isort]
       profile = "black"
       multi_line_output = 3
       
       [tool.mypy]
       python_version = "3.11"
       warn_return_any = true
       warn_unused_configs = true
       disallow_untyped_defs = true
       
       [tool.pytest.ini_options]
       testpaths = ["tests"]
       python_files = ["test_*.py"]
       python_classes = ["Test*"]
       python_functions = ["test_*"]
       addopts = "--cov=app --cov-report=html --cov-report=term-missing"
       ```

    3. **Pre-commit配置**：
       ```yaml
       # .pre-commit-config.yaml
       repos:
         - repo: https://github.com/psf/black
           rev: 23.9.1
           hooks:
             - id: black
         - repo: https://github.com/pycqa/isort
           rev: 5.12.0
           hooks:
             - id: isort
         - repo: https://github.com/pycqa/flake8
           rev: 6.1.0
           hooks:
             - id: flake8
         - repo: https://github.com/pre-commit/mirrors-mypy
           rev: v1.6.1
           hooks:
             - id: mypy
       ```

    ### Phase 2: 代码编写标准
    1. **函数设计标准**：
       ```python
       from typing import List, Optional, Dict, Any
       from datetime import datetime
       
       def process_user_data(
           user_id: int,
           data: Dict[str, Any],
           options: Optional[List[str]] = None
       ) -> Dict[str, Any]:
           """
           处理用户数据的业务逻辑函数
           
           Args:
               user_id: 用户ID
               data: 用户数据字典
               options: 可选的处理选项列表
               
           Returns:
               处理后的数据字典
               
           Raises:
               ValueError: 当用户ID无效时
               KeyError: 当必需的数据字段缺失时
           """
           if user_id <= 0:
               raise ValueError("用户ID必须为正整数")
           
           # 业务逻辑实现
           result = {"user_id": user_id, "processed_at": datetime.utcnow()}
           return result
       ```

    2. **类设计标准**：
       ```python
       from abc import ABC, abstractmethod
       from typing import Protocol
       
       class UserRepository(Protocol):
           """用户数据访问接口协议"""
           
           def get_by_id(self, user_id: int) -> Optional[User]:
               """根据ID获取用户"""
               ...
           
           def save(self, user: User) -> User:
               """保存用户数据"""
               ...
       
       class UserService:
           """用户业务服务类"""
           
           def __init__(self, repository: UserRepository) -> None:
               """
               初始化用户服务
               
               Args:
                   repository: 用户数据访问对象
               """
               self._repository = repository
           
           def create_user(self, user_data: UserCreateData) -> User:
               """创建新用户"""
               # 实现细节
               pass
       ```

    3. **异常处理标准**：
       ```python
       import logging
       from typing import Optional
       
       logger = logging.getLogger(__name__)
       
       class UserServiceError(Exception):
           """用户服务基础异常"""
           pass
       
       class UserNotFoundError(UserServiceError):
           """用户未找到异常"""
           pass
       
       def get_user_safely(user_id: int) -> Optional[User]:
           """安全获取用户信息"""
           try:
               user = user_repository.get_by_id(user_id)
               if user is None:
                   logger.warning(f"用户不存在: {user_id}")
                   raise UserNotFoundError(f"用户ID {user_id} 不存在")
               return user
           except DatabaseError as e:
               logger.error(f"数据库查询失败: {e}")
               raise UserServiceError("用户查询失败") from e
       ```

    ### Phase 3: 测试质量标准
    1. **单元测试标准**：
       ```python
       import pytest
       from unittest.mock import Mock, patch
       
       class TestUserService:
           """用户服务测试类"""
           
           def setup_method(self):
               """测试前置设置"""
               self.mock_repository = Mock(spec=UserRepository)
               self.user_service = UserService(self.mock_repository)
           
           def test_create_user_success(self):
               """测试成功创建用户"""
               # Given
               user_data = UserCreateData(username="test", email="<EMAIL>")
               expected_user = User(id=1, username="test", email="<EMAIL>")
               self.mock_repository.save.return_value = expected_user
               
               # When
               result = self.user_service.create_user(user_data)
               
               # Then
               assert result.username == "test"
               assert result.email == "<EMAIL>"
               self.mock_repository.save.assert_called_once()
           
           def test_create_user_with_invalid_data(self):
               """测试使用无效数据创建用户"""
               # Given
               invalid_data = UserCreateData(username="", email="invalid-email")
               
               # When & Then
               with pytest.raises(ValidationError):
                   self.user_service.create_user(invalid_data)
       ```

    2. **集成测试标准**：
       ```python
       import pytest
       from fastapi.testclient import TestClient
       from sqlalchemy import create_engine
       from sqlalchemy.orm import sessionmaker
       
       @pytest.fixture
       def test_db():
           """测试数据库fixture"""
           engine = create_engine("sqlite:///./test.db")
           TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
           Base.metadata.create_all(bind=engine)
           
           db = TestingSessionLocal()
           try:
               yield db
           finally:
               db.close()
               Base.metadata.drop_all(bind=engine)
       
       def test_create_user_endpoint(client: TestClient, test_db):
           """测试创建用户API端点"""
           response = client.post("/users/", json={
               "username": "testuser",
               "email": "<EMAIL>"
           })
           
           assert response.status_code == 201
           data = response.json()
           assert data["username"] == "testuser"
           assert "id" in data
       ```

    ### Phase 4: 持续质量监控
    1. **质量指标监控**：
       ```python
       # 代码覆盖率报告
       pytest --cov=app --cov-report=html --cov-report=term-missing
       
       # 代码复杂度分析
       radon cc app/ -a -nc
       
       # 代码重复检测
       pylint --load-plugins=pylint.extensions.check_elif app/
       ```

    2. **CI/CD集成**：
       ```yaml
       # .github/workflows/quality.yml
       name: Code Quality
       on: [push, pull_request]
       
       jobs:
         quality:
           runs-on: ubuntu-latest
           steps:
             - uses: actions/checkout@v3
             - name: Set up Python
               uses: actions/setup-python@v4
               with:
                 python-version: '3.11'
             - name: Install dependencies
               run: |
                 pip install -r requirements-dev.txt
             - name: Run quality checks
               run: |
                 black --check app/
                 isort --check-only app/
                 flake8 app/
                 mypy app/
                 pytest --cov=app --cov-fail-under=80
       ```
  </process>

  <criteria>
    ## 代码质量评价标准

    ### 代码风格
    - ✅ 100% PEP 8合规性（通过black/flake8检查）
    - ✅ 一致的命名约定和代码结构
    - ✅ 适当的代码注释和文档
    - ✅ 合理的函数和类长度
    - ✅ 清晰的模块组织结构

    ### 类型安全
    - ✅ 90%以上的类型注解覆盖率
    - ✅ 通过mypy静态类型检查
    - ✅ 正确使用泛型和协议
    - ✅ 适当的类型别名定义
    - ✅ 类型安全的API设计

    ### 测试质量
    - ✅ 单元测试覆盖率 ≥ 80%
    - ✅ 集成测试覆盖关键业务流程
    - ✅ 测试用例设计合理且可维护
    - ✅ 适当的测试数据和mock使用
    - ✅ 性能测试覆盖关键路径

    ### 安全性
    - ✅ 通过bandit安全扫描
    - ✅ 依赖包安全性检查（safety）
    - ✅ 输入验证和输出编码
    - ✅ 适当的错误处理和日志记录
    - ✅ 敏感信息保护措施

    ### 可维护性
    - ✅ 代码复杂度控制在合理范围
    - ✅ 低耦合高内聚的设计
    - ✅ 清晰的依赖关系
    - ✅ 完整的文档和注释
    - ✅ 易于扩展和修改的架构
  </criteria>
</execution>
