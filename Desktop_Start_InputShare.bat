@echo off
title InputShare Desktop Launcher

echo ========================================
echo       InputShare Desktop Launcher
echo ========================================
echo.

REM Set project path (modify according to your actual path)
set PROJECT_PATH=C:\Users\<USER>\githubProject\InputShare

echo [INFO] Project path: %PROJECT_PATH%
echo [INFO] Starting InputShare...
echo.

REM Check if project path exists
if not exist "%PROJECT_PATH%" (
    echo [ERROR] Project path does not exist: %PROJECT_PATH%
    echo [INFO] Please modify the PROJECT_PATH variable in this .bat file
    echo.
    pause
    exit /b 1
)

REM Check if main.py exists
if not exist "%PROJECT_PATH%\main.py" (
    echo [ERROR] Cannot find main.py in project path
    echo [INFO] Please confirm the project path is correct
    echo.
    pause
    exit /b 1
)

REM Switch to project directory and run
cd /d "%PROJECT_PATH%"
python main.py

REM Error handling
if %ERRORLEVEL% neq 0 (
    echo.
    echo [ERROR] Program exited with error code: %ERRORLEVEL%
    echo [INFO] Please check:
    echo   1. Python is properly installed
    echo   2. Dependencies are installed: pip install -r requirements.txt
    echo   3. Device connection is normal
    echo.
    pause
) else (
    echo.
    echo [INFO] Program exited normally
)
