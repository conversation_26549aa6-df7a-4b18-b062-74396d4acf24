# FAQs

## How to enable wireless debugging quickly?

You can refer to this video to add the Wireless Debugging to Quick Setting Shortcut:
[How To Add / Remove Wireless Debugging Quick Setting Shortcut](https://www.youtube.com/watch?v=_MMpawUGeKI)

## Does it share the screen of Android device to the computer?

No, __InputShare__ only shares the keyboard and mouse, it will not mirror the screen of your Android device to your computer.

## Why can't I use the FX function keys described in the [Shortcuts](./shortcuts_en.md) section after connecting my device?

If you are using a heavily customized Android system (e.g., MIUI or HyperOS), please try the following steps:

1. Open the Developer Options on your device.
2. Enable __USB Debugging__ and __USB Debugging (Security settings)__.

After completing these settings, try using the shortcut keys again.

## Do I need to configure ADB by myself?

No, there is a built-in ADB packaged, which will be called automatically.