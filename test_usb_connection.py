#!/usr/bin/env python3
"""
USB连接测试脚本
专门测试鸿蒙设备的USB调试连接
"""

import subprocess
import sys
import time
from pathlib import Path

def run_adb_command(command: list, timeout: int = 10) -> tuple[bool, str]:
    """执行ADB命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            cwd=Path(__file__).parent
        )
        return result.returncode == 0, result.stdout + result.stderr
    except subprocess.TimeoutExpired:
        return False, "命令超时"
    except Exception as e:
        return False, f"执行错误: {str(e)}"

def check_adb_server():
    """检查ADB服务器状态"""
    print("🔧 检查ADB服务器...")
    
    # 重启ADB服务器
    run_adb_command([r".\adb-bin\adb.exe", "kill-server"])
    time.sleep(1)
    
    success, output = run_adb_command([r".\adb-bin\adb.exe", "start-server"])
    if success:
        print("✅ ADB服务器启动成功")
        return True
    else:
        print(f"❌ ADB服务器启动失败: {output}")
        return False

def detect_usb_devices():
    """检测USB连接的设备"""
    print("📱 检测USB连接的设备...")
    
    success, output = run_adb_command([r".\adb-bin\adb.exe", "devices", "-l"])
    if success:
        print(f"ADB输出:\n{output}")
        
        lines = output.strip().split('\n')
        devices = []
        
        for line in lines:
            if line and not line.startswith('List of devices') and '\t' in line:
                devices.append(line)
        
        if devices:
            print(f"✅ 检测到 {len(devices)} 个设备:")
            for i, device in enumerate(devices, 1):
                parts = device.split('\t')
                device_id = parts[0]
                status = parts[1] if len(parts) > 1 else "unknown"
                print(f"   {i}. 设备ID: {device_id}")
                print(f"      状态: {status}")
                if len(parts) > 2:
                    print(f"      详情: {parts[2]}")
            return devices
        else:
            print("⚠️  未检测到任何设备")
            return []
    else:
        print(f"❌ 设备检测失败: {output}")
        return []

def test_device_properties(device_id: str):
    """测试设备属性"""
    print(f"\n🔍 测试设备属性 (设备: {device_id})...")
    
    cmd_base = [r".\adb-bin\adb.exe", "-s", device_id]
    
    # 测试基本连接
    print("   测试基本连接...")
    success, output = run_adb_command(cmd_base + ["shell", "echo", "hello"], timeout=5)
    if success:
        print("   ✅ 基本连接正常")
    else:
        print(f"   ❌ 基本连接失败: {output}")
        return False
    
    # 获取设备信息
    properties = [
        ("ro.product.model", "设备型号"),
        ("ro.product.brand", "设备品牌"),
        ("ro.product.manufacturer", "制造商"),
        ("ro.build.version.release", "Android版本"),
        ("ro.build.version.sdk", "SDK版本"),
        ("ro.harmonyos.version.name", "鸿蒙版本"),
        ("ro.build.display.id", "构建ID"),
    ]
    
    print("   获取设备信息:")
    for prop, desc in properties:
        success, output = run_adb_command(cmd_base + ["shell", "getprop", prop], timeout=5)
        if success and output.strip():
            print(f"   📱 {desc}: {output.strip()}")
        else:
            print(f"   ⚠️  {desc}: 无法获取")
    
    return True

def test_input_system(device_id: str):
    """测试输入系统"""
    print(f"\n⌨️  测试输入系统 (设备: {device_id})...")
    
    cmd_base = [r".\adb-bin\adb.exe", "-s", device_id]
    
    # 检查input命令
    success, output = run_adb_command(cmd_base + ["shell", "input"], timeout=5)
    if success:
        print("   ✅ input命令可用")
        if "usage:" in output.lower() or "keyevent" in output.lower():
            print("   ✅ 支持键盘事件")
        if "tap" in output.lower():
            print("   ✅ 支持触摸事件")
    else:
        print(f"   ❌ input命令不可用: {output}")
        return False
    
    # 测试安全的输入命令
    print("   测试基本输入功能:")
    
    # 测试Home键（相对安全）
    print("   🏠 测试Home键...")
    success, output = run_adb_command(cmd_base + ["shell", "input", "keyevent", "KEYCODE_HOME"], timeout=5)
    if success:
        print("   ✅ Home键测试成功")
    else:
        print(f"   ❌ Home键测试失败: {output}")
    
    return True

def main():
    """主函数"""
    print("🚀 鸿蒙设备USB连接测试")
    print("=" * 40)
    
    print("📋 请确保:")
    print("1. 鸿蒙平板已启用开发者选项")
    print("2. 已启用USB调试")
    print("3. 使用USB数据线连接平板到电脑")
    print("4. 在平板上允许USB调试授权")
    print()
    
    input("准备就绪后按Enter键继续...")
    
    # 检查ADB服务器
    if not check_adb_server():
        return
    
    # 检测设备
    devices = detect_usb_devices()
    
    if not devices:
        print("\n❌ 未检测到设备，请检查:")
        print("   1. USB线是否正确连接")
        print("   2. 设备是否启用了USB调试")
        print("   3. 是否在设备上授权了此计算机")
        print("   4. 是否安装了正确的USB驱动")
        print("\n💡 提示: 有些鸿蒙设备可能需要华为官方驱动")
        return
    
    # 测试第一个设备
    device_id = devices[0].split('\t')[0]
    print(f"\n🎯 测试设备: {device_id}")
    
    if test_device_properties(device_id):
        test_input_system(device_id)
        
        print(f"\n🎉 设备测试完成!")
        print(f"✅ 设备 {device_id} 可以与InputShare配合使用")
        print("\n💡 现在您可以运行 InputShare 主程序:")
        print("   python main.py")
    else:
        print(f"\n❌ 设备测试失败")

if __name__ == "__main__":
    main()
