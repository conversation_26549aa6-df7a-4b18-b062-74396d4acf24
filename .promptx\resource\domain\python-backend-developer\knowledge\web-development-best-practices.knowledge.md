# Web开发最佳实践知识体系

## 🏗️ API设计原则

### RESTful API设计
```python
# 资源命名规范
GET    /users           # 获取用户列表
GET    /users/{id}      # 获取特定用户
POST   /users           # 创建新用户
PUT    /users/{id}      # 更新用户（完整更新）
PATCH  /users/{id}      # 部分更新用户
DELETE /users/{id}      # 删除用户

# 嵌套资源
GET    /users/{id}/posts        # 获取用户的文章
POST   /users/{id}/posts        # 为用户创建文章
GET    /users/{id}/posts/{pid}  # 获取用户的特定文章
```

### HTTP状态码规范
```python
from fastapi import HTTPException, status

# 成功响应
200  # OK - 请求成功
201  # Created - 资源创建成功
204  # No Content - 删除成功

# 客户端错误
400  # Bad Request - 请求参数错误
401  # Unauthorized - 未认证
403  # Forbidden - 无权限
404  # Not Found - 资源不存在
409  # Conflict - 资源冲突
422  # Unprocessable Entity - 数据验证失败

# 服务器错误
500  # Internal Server Error - 服务器内部错误
502  # Bad Gateway - 网关错误
503  # Service Unavailable - 服务不可用

# 使用示例
@app.get("/users/{user_id}")
async def get_user(user_id: int):
    user = await user_service.get_by_id(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    return user
```

### API版本控制
```python
# URL路径版本控制
@app.get("/api/v1/users")
async def get_users_v1():
    pass

@app.get("/api/v2/users")
async def get_users_v2():
    pass

# 请求头版本控制
from fastapi import Header

@app.get("/users")
async def get_users(api_version: str = Header(default="v1", alias="API-Version")):
    if api_version == "v1":
        return await get_users_v1_logic()
    elif api_version == "v2":
        return await get_users_v2_logic()
    else:
        raise HTTPException(400, "不支持的API版本")
```

## 🔐 安全最佳实践

### 输入验证与清理
```python
from pydantic import BaseModel, validator, Field
import re
import html

class UserInput(BaseModel):
    username: str = Field(..., min_length=3, max_length=50)
    email: str = Field(..., regex=r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    content: str = Field(..., max_length=1000)
    
    @validator('username')
    def validate_username(cls, v):
        # 只允许字母、数字、下划线
        if not re.match(r'^[a-zA-Z0-9_]+$', v):
            raise ValueError('用户名只能包含字母、数字和下划线')
        return v
    
    @validator('content')
    def sanitize_content(cls, v):
        # HTML转义防止XSS
        return html.escape(v)
```

### SQL注入防护
```python
from sqlalchemy.orm import Session
from sqlalchemy import text

# ❌ 错误做法 - 容易SQL注入
def get_user_by_name_unsafe(db: Session, username: str):
    query = f"SELECT * FROM users WHERE username = '{username}'"
    return db.execute(text(query)).fetchone()

# ✅ 正确做法 - 使用参数化查询
def get_user_by_name_safe(db: Session, username: str):
    query = text("SELECT * FROM users WHERE username = :username")
    return db.execute(query, {"username": username}).fetchone()

# ✅ 使用ORM（推荐）
def get_user_by_name_orm(db: Session, username: str):
    return db.query(User).filter(User.username == username).first()
```

### 密码安全
```python
from passlib.context import CryptContext
import secrets

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class PasswordService:
    @staticmethod
    def hash_password(password: str) -> str:
        return pwd_context.hash(password)
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def generate_secure_token(length: int = 32) -> str:
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def validate_password_strength(password: str) -> bool:
        """验证密码强度"""
        if len(password) < 8:
            return False
        if not re.search(r'[A-Z]', password):
            return False
        if not re.search(r'[a-z]', password):
            return False
        if not re.search(r'\d', password):
            return False
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            return False
        return True
```

## 📊 性能优化策略

### 数据库优化
```python
from sqlalchemy import Index
from sqlalchemy.orm import selectinload, joinedload

class User(Base):
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), nullable=False)
    email = Column(String(100), nullable=False)
    
    # 添加索引
    __table_args__ = (
        Index('ix_users_username_email', 'username', 'email'),
    )

# N+1查询问题解决
def get_users_with_posts_efficient(db: Session):
    # 使用joinedload预加载关联数据
    return db.query(User).options(joinedload(User.posts)).all()

# 分页查询
def get_users_paginated(db: Session, page: int = 1, size: int = 20):
    offset = (page - 1) * size
    return db.query(User).offset(offset).limit(size).all()
```

### 缓存策略
```python
import redis
from functools import wraps
import json
import hashlib

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_result(expire_time: int = 3600):
    """缓存装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{func.__name__}:{hashlib.md5(str(args + tuple(kwargs.items())).encode()).hexdigest()}"
            
            # 尝试从缓存获取
            cached_result = redis_client.get(cache_key)
            if cached_result:
                return json.loads(cached_result)
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            redis_client.setex(cache_key, expire_time, json.dumps(result, default=str))
            return result
        return wrapper
    return decorator

@cache_result(expire_time=1800)
async def get_user_profile(user_id: int):
    # 耗时的数据库查询
    return await complex_user_query(user_id)
```

### 异步处理
```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

# CPU密集型任务使用线程池
executor = ThreadPoolExecutor(max_workers=4)

async def cpu_intensive_task(data):
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(executor, process_data, data)

# 批量处理
async def batch_process_users(user_ids: list):
    semaphore = asyncio.Semaphore(10)  # 限制并发数
    
    async def process_single_user(user_id):
        async with semaphore:
            return await process_user(user_id)
    
    tasks = [process_single_user(uid) for uid in user_ids]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    return results
```

## 🧪 测试策略

### 测试金字塔
```python
# 单元测试 (70%)
def test_password_hashing():
    password = "test_password"
    hashed = PasswordService.hash_password(password)
    assert PasswordService.verify_password(password, hashed)
    assert not PasswordService.verify_password("wrong_password", hashed)

# 集成测试 (20%)
@pytest.mark.asyncio
async def test_user_creation_flow(async_client, test_db):
    # 测试完整的用户创建流程
    response = await async_client.post("/users/", json={
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "TestPass123!"
    })
    assert response.status_code == 201
    
    # 验证数据库中的数据
    user = test_db.query(User).filter(User.username == "testuser").first()
    assert user is not None
    assert user.email == "<EMAIL>"

# 端到端测试 (10%)
@pytest.mark.e2e
async def test_complete_user_journey(async_client):
    # 注册 -> 登录 -> 更新资料 -> 删除账户
    # 完整的用户使用流程测试
    pass
```

### 测试数据管理
```python
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

@pytest.fixture(scope="function")
def test_db():
    """每个测试函数使用独立的数据库"""
    engine = create_engine("sqlite:///./test.db")
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)

@pytest.fixture
def sample_user():
    """测试用户数据"""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "TestPass123!"
    }
```

## 📝 日志与监控

### 结构化日志
```python
import structlog
import logging
from datetime import datetime

# 配置结构化日志
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.JSONRenderer()
    ],
    wrapper_class=structlog.stdlib.BoundLogger,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# 使用示例
async def process_payment(user_id: int, amount: float):
    log = logger.bind(user_id=user_id, amount=amount, operation="payment")
    log.info("开始处理支付")
    
    try:
        result = await payment_service.process(user_id, amount)
        log.info("支付处理成功", transaction_id=result.id)
        return result
    except PaymentError as e:
        log.error("支付处理失败", error=str(e), error_code=e.code)
        raise
```

### 健康检查
```python
from fastapi import FastAPI, status
from sqlalchemy import text

@app.get("/health", status_code=status.HTTP_200_OK)
async def health_check():
    """应用健康检查"""
    checks = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "checks": {}
    }
    
    # 数据库连接检查
    try:
        db.execute(text("SELECT 1"))
        checks["checks"]["database"] = "healthy"
    except Exception as e:
        checks["checks"]["database"] = f"unhealthy: {str(e)}"
        checks["status"] = "unhealthy"
    
    # Redis连接检查
    try:
        redis_client.ping()
        checks["checks"]["redis"] = "healthy"
    except Exception as e:
        checks["checks"]["redis"] = f"unhealthy: {str(e)}"
        checks["status"] = "unhealthy"
    
    return checks
```

这些最佳实践涵盖了Python Web开发的关键方面，从API设计到安全性、性能优化、测试和监控，为构建高质量的Web应用提供了全面的指导。
